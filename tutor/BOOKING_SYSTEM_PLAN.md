# Booking System Implementation Plan

## Phase 1: Basic Booking Structure ✅ (Current)
- [x] Tu<PERSON> profile pages with booking sidebar
- [x] Calendar UI with date/time selection
- [x] Session duration selection
- [x] Pricing display

## Phase 2: Database Schema (Next)
### Tables to Create:
1. **bookings**
   - id (UUID, primary key)
   - student_id (UUID, references profiles.id)
   - tutor_id (UUID, references profiles.id)
   - session_date (DATE)
   - session_time (TIME)
   - duration_minutes (INTEGER)
   - status (ENUM: 'pending', 'confirmed', 'completed', 'cancelled')
   - total_amount (INTEGER, in cents)
   - created_at (TIMESTAMP)
   - updated_at (TIMESTAMP)

2. **tutor_availability**
   - id (UUID, primary key)
   - tutor_id (UUID, references profiles.id)
   - day_of_week (INTEGER, 0-6)
   - start_time (TIME)
   - end_time (TIME)
   - is_available (BOOLEAN)
   - created_at (TIMESTAMP)

3. **booking_payments**
   - id (UUID, primary key)
   - booking_id (UUID, references bookings.id)
   - stripe_payment_intent_id (VARCHAR)
   - amount (INTEGER, in cents)
   - status (ENUM: 'pending', 'succeeded', 'failed', 'refunded')
   - created_at (TIMESTAMP)

## Phase 3: Core Booking Functionality
### Features to Implement:
1. **Availability Management**
   - Tutors can set their weekly availability
   - Block out unavailable times
   - Show only available slots to students

2. **Booking Process**
   - Student selects date/time
   - System checks availability
   - Creates pending booking
   - Redirects to payment

3. **Payment Integration**
   - Stripe integration for payments
   - Handle payment success/failure
   - Update booking status

4. **Notifications**
   - Email confirmations
   - Reminder emails
   - SMS notifications (optional)

## Phase 4: Advanced Features
1. **Calendar Integration**
   - Google Calendar sync
   - iCal export
   - Automatic calendar invites

2. **Session Management**
   - Video call integration (Zoom/Meet)
   - Session notes
   - File sharing

3. **Booking Management**
   - Reschedule bookings
   - Cancel bookings
   - Refund handling

## Phase 5: Analytics & Optimization
1. **Dashboard Analytics**
   - Booking statistics
   - Revenue tracking
   - Student retention metrics

2. **Automated Features**
   - Smart scheduling suggestions
   - Dynamic pricing
   - Automated follow-ups

## Implementation Priority:
1. **Week 1**: Database schema + basic availability
2. **Week 2**: Booking creation + payment flow
3. **Week 3**: Booking management + notifications
4. **Week 4**: Calendar integration + polish

## Technical Stack:
- **Frontend**: Next.js + TypeScript
- **Backend**: Supabase (PostgreSQL)
- **Payments**: Stripe
- **Calendar**: Google Calendar API
- **Notifications**: Resend/SendGrid
- **Video**: Zoom SDK (future)
