-- Fix set_tutor_verification Function Conflict
-- Issue: Multiple function signatures causing PGRST203 error
-- Solution: Drop all versions and recreate with single signature

-- Step 1: Check what functions currently exist
SELECT 'Current set_tutor_verification functions:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types,
    pronargs as arg_count,
    proargnames as parameter_names
FROM pg_proc 
WHERE proname = 'set_tutor_verification';

-- Step 2: Drop ALL possible variations
DROP FUNCTION IF EXISTS public.set_tutor_verification(UUID, boolean) CASCADE;
DROP FUNCTION IF EXISTS public.set_tutor_verification(UUID, boolean, UUID) CASCADE;
DROP FUNCTION IF EXISTS set_tutor_verification(UUID, boolean) CASCADE;
DROP FUNCTION IF EXISTS set_tutor_verification(UUID, boolean, UUID) CASCADE;

-- Step 3: Recreate the function with the correct signature
CREATE OR REPLACE FUNCTION public.set_tutor_verification(tutor_id UUID, verified boolean)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    UPDATE public.profiles 
    SET 
        tutor_status = CASE 
            WHEN verified THEN 'approved'::approval_status
            ELSE 'pending'::approval_status
        END,
        updated_at = NOW()
    WHERE id = tutor_id
    AND 'tutor' = ANY(COALESCE(roles, '{}'));
END;
$$;

-- Step 4: Grant permissions
GRANT EXECUTE ON FUNCTION public.set_tutor_verification(UUID, boolean) TO authenticated;

-- Step 5: Verify the fix
SELECT 'After cleanup - set_tutor_verification functions:' as status;
SELECT 
    proname,
    oidvectortypes(proargtypes) as argument_types,
    pronargs as arg_count,
    proargnames as parameter_names
FROM pg_proc 
WHERE proname = 'set_tutor_verification';

-- Step 6: Test the function works
SELECT 'Testing function exists and is callable...' as status;

-- Final status
SELECT 'set_tutor_verification function conflict resolved!' as status;
