-- Update tutor specialties with realistic, diverse content
-- This script replaces generic specialty descriptions with subject-specific ones

-- Clear existing specialties and add realistic ones
DELETE FROM tutor_specialties WHERE tutor_id IN (
  SELECT id FROM profiles WHERE roles @> '["tutor"]'
);

-- Insert Mathematics specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Mathematics' as subject,
  'High School' as level,
  '["Algebra", "Geometry", "Trigonometry", "Pre-Calculus"]'::jsonb as topics,
  'Expert guidance in foundational math concepts with focus on problem-solving strategies and exam preparation.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 0;

INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Calculus' as subject,
  'College' as level,
  '["Differential Calculus", "Integral Calculus", "Multivariable Calculus"]'::jsonb as topics,
  'Advanced calculus tutoring with emphasis on conceptual understanding and practical applications.'
FROM profiles 
WHERE roles @> '["tutor"]' 
<PERSON><PERSON><PERSON> BY created_at 
LIMIT 1 OFFSET 0;

-- Insert Spanish specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Spanish' as subject,
  'All Levels' as level,
  '["Conversation", "Grammar", "Vocabulary", "Cultural Studies"]'::jsonb as topics,
  'Native speaker offering immersive Spanish lessons with cultural context and practical conversation skills.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 1;

-- Insert Physics specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Physics' as subject,
  'High School' as level,
  '["Mechanics", "Waves", "Electricity", "Magnetism"]'::jsonb as topics,
  'Comprehensive physics instruction using visual demonstrations and real-world examples to clarify complex concepts.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 2;

INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Advanced Physics' as subject,
  'College' as level,
  '["Quantum Mechanics", "Thermodynamics", "Statistical Physics"]'::jsonb as topics,
  'PhD-level expertise in advanced physics topics with research experience and academic insight.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 2;

-- Insert English specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'English Literature' as subject,
  'High School' as level,
  '["Poetry Analysis", "Novel Studies", "Essay Writing", "Literary Criticism"]'::jsonb as topics,
  'Passionate literature instruction focusing on critical analysis, writing skills, and appreciation of classic works.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 3;

INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Creative Writing' as subject,
  'All Levels' as level,
  '["Short Stories", "Poetry", "Character Development", "Plot Structure"]'::jsonb as topics,
  'Professional writer guiding students through creative expression and storytelling techniques.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 3;

-- Insert Computer Science specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Programming' as subject,
  'Beginner to Intermediate' as level,
  '["Python", "JavaScript", "HTML/CSS", "Data Structures"]'::jsonb as topics,
  'Hands-on coding instruction with practical projects and industry best practices for aspiring developers.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 4;

-- Insert Chemistry specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'Chemistry' as subject,
  'High School' as level,
  '["General Chemistry", "Organic Chemistry", "Chemical Reactions", "Lab Techniques"]'::jsonb as topics,
  'Certified chemistry teacher making complex reactions understandable through visual aids and experiments.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 5;

-- Insert History specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'European History' as subject,
  'High School' as level,
  '["Medieval Period", "Renaissance", "World Wars", "Modern Europe"]'::jsonb as topics,
  'Engaging historical instruction bringing past events to life through storytelling and critical analysis.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 6;

-- Insert French specialties
INSERT INTO tutor_specialties (tutor_id, subject, level, topics, description)
SELECT 
  id as tutor_id,
  'French' as subject,
  'All Levels' as level,
  '["Pronunciation", "Grammar", "Conversation", "French Culture"]'::jsonb as topics,
  'Native French speaker providing authentic language instruction with cultural immersion and accent training.'
FROM profiles 
WHERE roles @> '["tutor"]' 
ORDER BY created_at 
LIMIT 1 OFFSET 7;
