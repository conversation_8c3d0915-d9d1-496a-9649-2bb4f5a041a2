const { v2: cloudinary } = require('cloudinary')
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

// Configure Supabase
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

// Sample profile pictures from Unsplash (free to use)
const SAMPLE_IMAGES = [
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=400&h=400&fit=crop&crop=face',
  'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400&h=400&fit=crop&crop=face'
]

async function uploadDummyProfiles() {
  try {
    console.log('🚀 Starting dummy profile picture upload...')

    // Get all tutors without profile pictures
    const { data: tutors, error } = await supabase
      .from('profiles')
      .select('id, full_name, profile_picture_url')
      .contains('roles', ['tutor'])
      .is('profile_picture_url', null)

    if (error) {
      throw error
    }

    if (!tutors || tutors.length === 0) {
      console.log('✅ No tutors found without profile pictures')
      return
    }

    console.log(`📋 Found ${tutors.length} tutors without profile pictures`)

    for (let i = 0; i < tutors.length; i++) {
      const tutor = tutors[i]
      const imageUrl = SAMPLE_IMAGES[i % SAMPLE_IMAGES.length]
      
      try {
        console.log(`📸 Uploading image for ${tutor.full_name}...`)

        // Upload image to Cloudinary
        const result = await cloudinary.uploader.upload(imageUrl, {
          folder: 'tutor-platform/users/profile-pictures',
          public_id: `${tutor.id}_${Date.now()}`,
          transformation: {
            width: 400,
            height: 400,
            crop: 'fill',
            gravity: 'face',
            quality: 'auto',
            format: 'webp'
          },
          overwrite: true,
          resource_type: 'image'
        })

        // Update tutor profile with new image URL
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ profile_picture_url: result.secure_url })
          .eq('id', tutor.id)

        if (updateError) {
          console.error(`❌ Failed to update ${tutor.full_name}:`, updateError.message)
        } else {
          console.log(`✅ Updated ${tutor.full_name} with profile picture`)
        }

        // Small delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 500))

      } catch (uploadError) {
        console.error(`❌ Failed to upload image for ${tutor.full_name}:`, uploadError.message)
      }
    }

    console.log('\n🎉 Dummy profile picture upload complete!')
    console.log('\n📋 Summary:')
    console.log(`  - Processed ${tutors.length} tutors`)
    console.log('  - Images uploaded to Cloudinary with proper transformations')
    console.log('  - Database updated with new profile picture URLs')
    
  } catch (error) {
    console.error('❌ Error uploading dummy profiles:', error)
    process.exit(1)
  }
}

// Function to list current tutors and their profile picture status
async function listTutorStatus() {
  try {
    const { data: tutors, error } = await supabase
      .from('profiles')
      .select('id, full_name, profile_picture_url')
      .contains('roles', ['tutor'])
      .order('full_name')

    if (error) throw error

    console.log('\n📋 Current Tutor Profile Picture Status:')
    console.log('=====================================')
    
    tutors.forEach(tutor => {
      const status = tutor.profile_picture_url ? '✅ Has Picture' : '❌ No Picture'
      console.log(`${tutor.full_name}: ${status}`)
    })

    const withPictures = tutors.filter(t => t.profile_picture_url).length
    const withoutPictures = tutors.filter(t => !t.profile_picture_url).length

    console.log('\n📊 Statistics:')
    console.log(`  Total tutors: ${tutors.length}`)
    console.log(`  With pictures: ${withPictures}`)
    console.log(`  Without pictures: ${withoutPictures}`)

  } catch (error) {
    console.error('❌ Error listing tutor status:', error)
  }
}

// Main function
async function main() {
  const command = process.argv[2]

  if (command === 'status') {
    await listTutorStatus()
  } else if (command === 'upload') {
    await uploadDummyProfiles()
  } else {
    console.log('📖 Usage:')
    console.log('  node scripts/upload-dummy-profiles.js status   - List current status')
    console.log('  node scripts/upload-dummy-profiles.js upload   - Upload dummy pictures')
  }
}

main()
