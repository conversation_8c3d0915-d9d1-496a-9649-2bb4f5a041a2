-- Update tutor profiles with realistic, diverse content
-- This script updates bio, subjects, and specialties for existing tutors

-- Update bio content with realistic descriptions
UPDATE profiles SET 
  bio = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN
      'Experienced mathematics tutor with 5+ years helping students excel in algebra, calculus, and statistics. I specialize in breaking down complex concepts into manageable steps and building confidence through practice.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN
      'Native Spanish speaker and certified language instructor. I help students develop conversational skills, grammar mastery, and cultural understanding through interactive lessons and real-world practice.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN
      'PhD in Physics with expertise in mechanics, thermodynamics, and quantum physics. I make challenging concepts accessible through visual demonstrations and practical applications.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' OR<PERSON><PERSON> <PERSON>Y created_at LIMIT 1 OFFSET 3) THEN
      'Professional writer and English literature graduate. I guide students through essay writing, literary analysis, and creative writing while building strong communication skills.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN
      'Computer Science graduate specializing in programming fundamentals, web development, and data structures. I help students build practical coding skills through hands-on projects.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN
      'Certified chemistry teacher with expertise in organic, inorganic, and analytical chemistry. I use visual aids and experiments to make chemistry engaging and understandable.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN
      'History enthusiast with MA in European History. I bring historical events to life through storytelling and help students develop critical thinking and analytical writing skills.'
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN
      'Bilingual French tutor with native fluency and teaching certification. I focus on pronunciation, grammar, and cultural immersion to help students achieve conversational confidence.'
    ELSE bio
  END
WHERE roles @> '["tutor"]';

-- Update subjects with diverse offerings
UPDATE profiles SET 
  subjects = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN
      '["Mathematics", "Algebra", "Calculus", "Statistics"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN
      '["Spanish", "Languages", "Conversation", "Grammar"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN
      '["Physics", "Science", "Mechanics", "Thermodynamics"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 3) THEN
      '["English", "Literature", "Writing", "Essay Writing"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN
      '["Computer Science", "Programming", "Web Development", "Python"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN
      '["Chemistry", "Science", "Organic Chemistry", "Lab Techniques"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN
      '["History", "European History", "World History", "Essay Writing"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN
      '["French", "Languages", "Pronunciation", "Culture"]'::jsonb
    ELSE subjects
  END
WHERE roles @> '["tutor"]';

-- Update languages with realistic combinations
UPDATE profiles SET 
  languages = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN
      '["English", "Mandarin"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN
      '["Spanish", "English", "Portuguese"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN
      '["English", "German"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 3) THEN
      '["English"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN
      '["English", "Hindi"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN
      '["English", "French"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN
      '["English", "Italian"]'::jsonb
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN
      '["French", "English", "Arabic"]'::jsonb
    ELSE languages
  END
WHERE roles @> '["tutor"]';

-- Update experience years with realistic values
UPDATE profiles SET 
  experience_years = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN 5
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN 7
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN 10
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 3) THEN 4
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN 3
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN 8
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN 6
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN 9
    ELSE experience_years
  END
WHERE roles @> '["tutor"]';

-- Update hourly rates with realistic pricing (in cents)
UPDATE profiles SET 
  hourly_rate = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN 4500 -- $45/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN 3500 -- $35/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN 6000 -- $60/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 3) THEN 4000 -- $40/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN 5500 -- $55/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN 5000 -- $50/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN 3800 -- $38/hr
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN 4200 -- $42/hr
    ELSE hourly_rate
  END
WHERE roles @> '["tutor"]';

-- Update response times with realistic values (in hours)
UPDATE profiles SET 
  response_time_hours = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN 2
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN 1
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN 4
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 3) THEN 3
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN 2
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN 6
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN 5
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN 3
    ELSE response_time_hours
  END
WHERE roles @> '["tutor"]';

-- Update session counts with realistic numbers
UPDATE profiles SET 
  total_sessions_taught = CASE 
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 0) THEN 156
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 1) THEN 203
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 2) THEN 89
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 3) THEN 127
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 4) THEN 94
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 5) THEN 178
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 6) THEN 142
    WHEN id = (SELECT id FROM profiles WHERE roles @> '["tutor"]' ORDER BY created_at LIMIT 1 OFFSET 7) THEN 211
    ELSE total_sessions_taught
  END
WHERE roles @> '["tutor"]';
