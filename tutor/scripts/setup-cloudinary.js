const { v2: cloudinary } = require('cloudinary')
require('dotenv').config({ path: '.env.local' })

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

async function setupCloudinary() {
  try {
    console.log('🚀 Setting up Cloudinary for tutor platform...')

    // Create upload preset for profile pictures
    const profilePreset = await cloudinary.api.create_upload_preset({
      name: 'tutor_profiles',
      unsigned: true, // Allow unsigned uploads from frontend
      folder: 'tutor-platform/users/profile-pictures',
      transformation: [
        {
          width: 400,
          height: 400,
          crop: 'fill',
          gravity: 'face',
          quality: 'auto',
          format: 'webp'
        }
      ],
      allowed_formats: ['jpg', 'jpeg', 'png', 'webp'],
      max_file_size: 5000000, // 5MB
      overwrite: true,
      unique_filename: true,
      use_filename: false,
    })

    console.log('✅ Created upload preset:', profilePreset.name)

    // Create upload preset for certificates
    const certificatePreset = await cloudinary.api.create_upload_preset({
      name: 'tutor_certificates',
      unsigned: true,
      folder: 'tutor-platform/tutors/certificates',
      transformation: [
        {
          width: 1200,
          height: 900,
          crop: 'fit',
          quality: 'auto',
          format: 'webp'
        }
      ],
      allowed_formats: ['jpg', 'jpeg', 'png', 'pdf'],
      max_file_size: 10000000, // 10MB
      overwrite: true,
      unique_filename: true,
    })

    console.log('✅ Created upload preset:', certificatePreset.name)

    // Create folders structure
    const folders = [
      'tutor-platform',
      'tutor-platform/users',
      'tutor-platform/users/profile-pictures',
      'tutor-platform/users/documents',
      'tutor-platform/tutors',
      'tutor-platform/tutors/certificates',
      'tutor-platform/tutors/portfolio',
      'tutor-platform/platform',
      'tutor-platform/platform/assets',
      'tutor-platform/platform/marketing',
      'tutor-platform/lessons',
      'tutor-platform/lessons/materials',
      'tutor-platform/lessons/recordings',
      'tutor-platform/students',
      'tutor-platform/students/submissions',
    ]

    console.log('📁 Creating folder structure...')
    
    // Create a dummy file in each folder to ensure they exist
    for (const folder of folders) {
      try {
        await cloudinary.uploader.upload(
          'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
          {
            folder: folder,
            public_id: '.gitkeep',
            overwrite: true,
            resource_type: 'image'
          }
        )
        console.log(`  ✅ Created folder: ${folder}`)
      } catch (error) {
        console.log(`  ⚠️  Folder may already exist: ${folder}`)
      }
    }

    console.log('\n🎉 Cloudinary setup complete!')
    console.log('\n📋 Summary:')
    console.log('  - Upload presets created: tutor_profiles, tutor_certificates')
    console.log('  - Folder structure established')
    console.log('  - Ready for profile picture uploads')
    
    console.log('\n🔗 Your Cloudinary URLs will follow this pattern:')
    console.log(`  Profile pictures: https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload/tutor-platform/users/profile-pictures/`)
    
  } catch (error) {
    console.error('❌ Error setting up Cloudinary:', error)
    process.exit(1)
  }
}

setupCloudinary()
