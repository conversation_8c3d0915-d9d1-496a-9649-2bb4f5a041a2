-- Update tutor availability with realistic schedules
-- Most tutors available until end of month, few until end of year

-- Clear existing availability
DELETE FROM tutor_availability WHERE tutor_id IN (
  SELECT id FROM profiles WHERE roles @> '["tutor"]'
);

-- Function to generate availability for a tutor
-- Most tutors: available until end of current month
-- Few tutors: available until end of year

-- Get tutor IDs in order
WITH tutor_ids AS (
  SELECT id, ROW_NUMBER() OVER (ORDER BY created_at) as rn
  FROM profiles 
  WHERE roles @> '["tutor"]'
)

-- Insert availability for each tutor
INSERT INTO tutor_availability (tutor_id, day_of_week, start_time, end_time, is_available)
SELECT 
  t.id as tutor_id,
  dow.day_of_week,
  CASE 
    -- Mathematics tutor (weekday evenings + weekends)
    WHEN t.rn = 1 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '18:00'::time -- Weekday evenings
        WHEN dow.day_of_week IN (0,6) THEN '09:00'::time -- Weekend mornings
      END
    -- Spanish tutor (flexible schedule)
    WHEN t.rn = 2 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,3,5) THEN '14:00'::time -- MWF afternoons
        WHEN dow.day_of_week IN (2,4) THEN '10:00'::time -- TTh mornings
        WHEN dow.day_of_week IN (0,6) THEN '16:00'::time -- Weekend afternoons
      END
    -- Physics tutor (academic schedule)
    WHEN t.rn = 3 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '15:00'::time -- Weekday afternoons
        ELSE NULL -- Weekends off
      END
    -- English tutor (morning person)
    WHEN t.rn = 4 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5,6) THEN '08:00'::time -- Mon-Sat mornings
        ELSE NULL -- Sunday off
      END
    -- CS tutor (tech schedule - late hours)
    WHEN t.rn = 5 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '19:00'::time -- Weekday evenings
        WHEN dow.day_of_week IN (0,6) THEN '13:00'::time -- Weekend afternoons
      END
    -- Chemistry tutor (standard schedule)
    WHEN t.rn = 6 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '16:00'::time -- Weekday afternoons
        WHEN dow.day_of_week = 6 THEN '10:00'::time -- Saturday mornings
        ELSE NULL -- Sunday off
      END
    -- History tutor (part-time)
    WHEN t.rn = 7 THEN 
      CASE 
        WHEN dow.day_of_week IN (2,4,6) THEN '17:00'::time -- TTh + Sat evenings
        ELSE NULL
      END
    -- French tutor (full availability)
    WHEN t.rn = 8 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '11:00'::time -- Weekday late mornings
        WHEN dow.day_of_week IN (0,6) THEN '14:00'::time -- Weekend afternoons
      END
    ELSE '16:00'::time
  END as start_time,
  
  CASE 
    -- Mathematics tutor
    WHEN t.rn = 1 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '21:00'::time -- 3 hour evening blocks
        WHEN dow.day_of_week IN (0,6) THEN '15:00'::time -- 6 hour weekend blocks
      END
    -- Spanish tutor
    WHEN t.rn = 2 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,3,5) THEN '18:00'::time -- 4 hour blocks
        WHEN dow.day_of_week IN (2,4) THEN '14:00'::time -- 4 hour blocks
        WHEN dow.day_of_week IN (0,6) THEN '20:00'::time -- 4 hour blocks
      END
    -- Physics tutor
    WHEN t.rn = 3 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '19:00'::time -- 4 hour blocks
        ELSE NULL
      END
    -- English tutor
    WHEN t.rn = 4 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5,6) THEN '13:00'::time -- 5 hour morning blocks
        ELSE NULL
      END
    -- CS tutor
    WHEN t.rn = 5 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '22:00'::time -- 3 hour evening blocks
        WHEN dow.day_of_week IN (0,6) THEN '18:00'::time -- 5 hour weekend blocks
      END
    -- Chemistry tutor
    WHEN t.rn = 6 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '20:00'::time -- 4 hour blocks
        WHEN dow.day_of_week = 6 THEN '15:00'::time -- 5 hour Saturday
        ELSE NULL
      END
    -- History tutor
    WHEN t.rn = 7 THEN 
      CASE 
        WHEN dow.day_of_week IN (2,4,6) THEN '20:00'::time -- 3 hour blocks
        ELSE NULL
      END
    -- French tutor
    WHEN t.rn = 8 THEN 
      CASE 
        WHEN dow.day_of_week IN (1,2,3,4,5) THEN '16:00'::time -- 5 hour blocks
        WHEN dow.day_of_week IN (0,6) THEN '19:00'::time -- 5 hour blocks
      END
    ELSE '20:00'::time
  END as end_time,
  
  true as is_available

FROM tutor_ids t
CROSS JOIN (
  SELECT generate_series(0, 6) as day_of_week
) dow
WHERE 
  -- Only insert rows where start_time is not NULL (tutor is available that day)
  CASE 
    WHEN t.rn = 1 THEN dow.day_of_week IN (0,1,2,3,4,5,6)
    WHEN t.rn = 2 THEN dow.day_of_week IN (0,1,2,3,4,5,6)
    WHEN t.rn = 3 THEN dow.day_of_week IN (1,2,3,4,5)
    WHEN t.rn = 4 THEN dow.day_of_week IN (1,2,3,4,5,6)
    WHEN t.rn = 5 THEN dow.day_of_week IN (0,1,2,3,4,5,6)
    WHEN t.rn = 6 THEN dow.day_of_week IN (1,2,3,4,5,6)
    WHEN t.rn = 7 THEN dow.day_of_week IN (2,4,6)
    WHEN t.rn = 8 THEN dow.day_of_week IN (0,1,2,3,4,5,6)
    ELSE true
  END;

-- Update availability end dates
-- Most tutors: until end of current month (70%)
-- Few tutors: until end of year (30%)
UPDATE profiles SET 
  availability_until = CASE 
    WHEN id IN (
      SELECT id FROM profiles 
      WHERE roles @> '["tutor"]' 
      ORDER BY created_at 
      LIMIT 2 -- First 2 tutors get end-of-year availability
    ) THEN DATE_TRUNC('year', CURRENT_DATE) + INTERVAL '1 year' - INTERVAL '1 day' -- End of current year
    ELSE DATE_TRUNC('month', CURRENT_DATE) + INTERVAL '1 month' - INTERVAL '1 day' -- End of current month
  END
WHERE roles @> '["tutor"]';
