-- Create booking_requests table for tutor booking workflow
-- This table stores initial booking requests before they become confirmed bookings

CREATE TABLE IF NOT EXISTS public.booking_requests (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  student_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  tutor_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  
  -- Request details
  requested_date DATE NOT NULL,
  requested_time TIME NOT NULL,
  subject TEXT NOT NULL,
  session_length INTEGER NOT NULL, -- in minutes
  message TEXT,
  
  -- Status and response
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'completed', 'cancelled')),
  decline_reason TEXT,
  
  -- Pricing
  total_amount INTEGER NOT NULL, -- in cents
  
  -- Audit fields
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.booking_requests ENABLE ROW LEVEL SECURITY;

-- R<PERSON> Policies
CREATE POLICY "Students can view their own booking requests" ON public.booking_requests
  FOR SELECT USING (student_id = auth.uid());

CREATE POLICY "Tutors can view booking requests for them" ON public.booking_requests
  FOR SELECT USING (tutor_id = auth.uid());

CREATE POLICY "Students can create booking requests" ON public.booking_requests
  FOR INSERT WITH CHECK (student_id = auth.uid());

CREATE POLICY "Tutors can update booking requests for them" ON public.booking_requests
  FOR UPDATE USING (tutor_id = auth.uid());

-- Indexes
CREATE INDEX IF NOT EXISTS idx_booking_requests_student_id ON booking_requests(student_id);
CREATE INDEX IF NOT EXISTS idx_booking_requests_tutor_id ON booking_requests(tutor_id);
CREATE INDEX IF NOT EXISTS idx_booking_requests_status ON booking_requests(status);
CREATE INDEX IF NOT EXISTS idx_booking_requests_date ON booking_requests(requested_date);

-- Grant permissions
GRANT ALL ON public.booking_requests TO authenticated;

SELECT 'booking_requests table created successfully!' as status;
