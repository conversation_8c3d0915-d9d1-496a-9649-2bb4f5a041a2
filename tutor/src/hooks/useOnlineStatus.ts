'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'

interface OnlineUser {
  user_id: string
  last_seen: string
  is_online: boolean
}

export function useOnlineStatus(userId?: string) {
  const [isOnline, setIsOnline] = useState(false)
  const [lastSeen, setLastSeen] = useState<Date | null>(null)
  const supabase = createClient()

  useEffect(() => {
    if (!userId) return

    // Check initial online status
    const checkOnlineStatus = async () => {
      try {
        const { data, error } = await supabase
          .from('user_presence')
          .select('last_seen, is_online')
          .eq('user_id', userId)
          .single()

        if (data && !error) {
          setIsOnline(data.is_online)
          setLastSeen(new Date(data.last_seen))
        } else {
          // User not in presence table, consider offline
          setIsOnline(false)
          setLastSeen(null)
        }
      } catch (error) {
        console.error('Error checking online status:', error)
        setIsOnline(false)
      }
    }

    checkOnlineStatus()

    // Subscribe to presence changes
    const channel = supabase
      .channel('user-presence')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_presence',
          filter: `user_id=eq.${userId}`
        },
        (payload) => {
          if (payload.new) {
            const newData = payload.new as OnlineUser
            setIsOnline(newData.is_online)
            setLastSeen(new Date(newData.last_seen))
          }
        }
      )
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }, [userId, supabase])

  return { isOnline, lastSeen }
}

export function useCurrentUserPresence() {
  const [user, setUser] = useState<any>(null)
  const supabase = createClient()

  useEffect(() => {
    // Get current user
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }

    getCurrentUser()

    // Update presence when user is active
    const updatePresence = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (user) {
        await supabase
          .from('user_presence')
          .upsert({
            user_id: user.id,
            last_seen: new Date().toISOString(),
            is_online: true
          })
      }
    }

    // Update presence immediately
    updatePresence()

    // Update presence every 30 seconds while active
    const interval = setInterval(updatePresence, 30000)

    // Update presence on page visibility change
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        updatePresence()
      } else {
        // Mark as offline when tab becomes hidden
        const markOffline = async () => {
          const { data: { user } } = await supabase.auth.getUser()
          if (user) {
            await supabase
              .from('user_presence')
              .upsert({
                user_id: user.id,
                last_seen: new Date().toISOString(),
                is_online: false
              })
          }
        }
        markOffline()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)

    // Cleanup
    return () => {
      clearInterval(interval)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      
      // Mark as offline when component unmounts
      const markOffline = async () => {
        const { data: { user } } = await supabase.auth.getUser()
        if (user) {
          await supabase
            .from('user_presence')
            .upsert({
              user_id: user.id,
              last_seen: new Date().toISOString(),
              is_online: false
            })
        }
      }
      markOffline()
    }
  }, [supabase])

  return user
}
