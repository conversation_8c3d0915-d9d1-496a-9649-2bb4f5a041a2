// Auto-generated deployment info
// This file is updated automatically on each deployment

export const DEPLOYMENT_INFO = {
  version: 'v1.2.7',
  commit: 'a06d1ab',
  timestamp: '2025-01-20T16:15:00.000Z',
  buildNumber: 27,
  status: 'deployed' as const
}

// Function to get current deployment info
export function getDeploymentInfo() {
  return {
    ...DEPLOYMENT_INFO,
    timestamp: new Date().toISOString(), // Always show current time for "deployed at"
    formattedTime: new Date().toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }
}

// Function to check if this is a new deployment
export function isNewDeployment() {
  if (typeof window === 'undefined') return false
  
  const lastSeenVersion = localStorage.getItem('lastSeenDeployment')
  const currentVersion = `${DEPLOYMENT_INFO.version}-${DEPLOYMENT_INFO.commit}`
  
  if (lastSeenVersion !== currentVersion) {
    localStorage.setItem('lastSeenDeployment', currentVersion)
    return true
  }
  
  return false
}
