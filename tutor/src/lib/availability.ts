export type WeeklyAvailabilityRange = {
  day_of_week: number // 0-6, Sunday=0
  start_time: string // 'HH:MM'
  end_time: string   // 'HH:MM'
  is_available?: boolean
}

export const DEFAULT_TIME_SLOTS = [
  '09:00','10:00','11:00','12:00','13:00','14:00',
  '15:00','16:00','17:00','18:00','19:00','20:00'
]

export function timeToMinutes(t: string): number {
  const [h, m] = t.split(':').map(Number)
  return h * 60 + m
}

export function isTimeWithinRange(time: string, start: string, end: string) {
  const tm = timeToMinutes(time)
  return tm >= timeToMinutes(start) && tm < timeToMinutes(end)
}

export function isSlotAvailableForDay(time: string, ranges: WeeklyAvailabilityRange[]): boolean {
  return ranges.some(r => (r.is_available ?? true) && isTimeWithinRange(time, r.start_time, r.end_time))
}

export function getDayOfWeek(dateISO: string): number {
  // Date.getDay(): 0 (Sun) - 6 (Sat)
  return new Date(dateISO + 'T00:00:00').getDay()
}

export function formatISO(date: Date): string {
  return date.toISOString().split('T')[0]
}

export function generateNextNDates(n: number): string[] {
  const out: string[] = []
  for (let i = 0; i < n; i++) {
    const d = new Date()
    d.setDate(d.getDate() + i + 1)
    out.push(formatISO(d))
  }
  return out
}

export function getAvailableSlotsForDate(dateISO: string, weeklyRanges: WeeklyAvailabilityRange[], timeSlots = DEFAULT_TIME_SLOTS): string[] {
  const dow = getDayOfWeek(dateISO)
  const rangesForDay = weeklyRanges.filter(r => r.day_of_week === dow)
  if (rangesForDay.length === 0) return []
  return timeSlots.filter(t => isSlotAvailableForDay(t, rangesForDay))
}

export function hasAnyAvailabilityForDate(dateISO: string, weeklyRanges: WeeklyAvailabilityRange[], timeSlots = DEFAULT_TIME_SLOTS): boolean {
  return getAvailableSlotsForDate(dateISO, weeklyRanges, timeSlots).length > 0
}

