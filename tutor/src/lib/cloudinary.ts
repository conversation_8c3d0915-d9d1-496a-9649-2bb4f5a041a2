import { v2 as cloudinary } from 'cloudinary'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

// Cloudinary folder structure for organized file management
export const CLOUDINARY_FOLDERS = {
  // User-related images
  PROFILE_PICTURES: 'tutor-platform/users/profile-pictures',
  USER_DOCUMENTS: 'tutor-platform/users/documents',
  
  // Tutor-specific content
  TUTOR_CERTIFICATES: 'tutor-platform/tutors/certificates',
  TUTOR_PORTFOLIO: 'tutor-platform/tutors/portfolio',
  
  // Platform content
  PLATFORM_ASSETS: 'tutor-platform/platform/assets',
  MARKETING_IMAGES: 'tutor-platform/platform/marketing',
  
  // Future expansions
  LESSON_MATERIALS: 'tutor-platform/lessons/materials',
  LESSON_RECORDINGS: 'tutor-platform/lessons/recordings',
  STUDENT_WORK: 'tutor-platform/students/submissions',
} as const

// Image transformation presets for different use cases
export const IMAGE_TRANSFORMATIONS = {
  PROFILE_PICTURE: {
    width: 400,
    height: 400,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto',
    format: 'webp',
  },
  PROFILE_PICTURE_SMALL: {
    width: 150,
    height: 150,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto',
    format: 'webp',
  },
  PROFILE_PICTURE_LARGE: {
    width: 800,
    height: 800,
    crop: 'fill',
    gravity: 'face',
    quality: 'auto',
    format: 'webp',
  },
  CERTIFICATE: {
    width: 1200,
    height: 900,
    crop: 'fit',
    quality: 'auto',
    format: 'webp',
  },
  DOCUMENT: {
    width: 1000,
    crop: 'fit',
    quality: 'auto',
    format: 'webp',
  },
} as const

// Helper function to generate Cloudinary URLs with transformations
export function getCloudinaryUrl(
  publicId: string, 
  transformation: keyof typeof IMAGE_TRANSFORMATIONS = 'PROFILE_PICTURE'
) {
  const baseUrl = `https://res.cloudinary.com/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`
  const transform = IMAGE_TRANSFORMATIONS[transformation]
  
  const transformString = Object.entries(transform)
    .map(([key, value]) => `${key}_${value}`)
    .join(',')
  
  return `${baseUrl}/${transformString}/${publicId}`
}

// Helper function to extract public ID from Cloudinary URL
export function extractPublicId(cloudinaryUrl: string): string | null {
  const match = cloudinaryUrl.match(/\/upload\/(?:v\d+\/)?(.+)$/)
  return match ? match[1].replace(/\.[^/.]+$/, '') : null
}

// Upload function for profile pictures (using unsigned upload)
export async function uploadProfilePicture(
  file: File,
  userId: string,
  folder: keyof typeof CLOUDINARY_FOLDERS = 'PROFILE_PICTURES'
): Promise<{ url: string; publicId: string }> {
  try {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('upload_preset', 'ml_default') // Using default unsigned preset for now
    formData.append('folder', CLOUDINARY_FOLDERS[folder])
    formData.append('public_id', `${userId}_${Date.now()}`)
    formData.append('transformation', 'c_fill,g_face,h_400,w_400,q_auto,f_webp')

    const response = await fetch(
      `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`,
      {
        method: 'POST',
        body: formData,
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error?.message || 'Failed to upload image')
    }

    const data = await response.json()
    return {
      url: data.secure_url,
      publicId: data.public_id,
    }
  } catch (error) {
    console.error('Error uploading to Cloudinary:', error)
    throw error
  }
}

// Delete image from Cloudinary
export async function deleteCloudinaryImage(publicId: string): Promise<boolean> {
  try {
    const result = await cloudinary.uploader.destroy(publicId)
    return result.result === 'ok'
  } catch (error) {
    console.error('Error deleting from Cloudinary:', error)
    return false
  }
}

export default cloudinary
