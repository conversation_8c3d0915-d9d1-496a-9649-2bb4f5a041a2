import { createClient } from '@/lib/supabase/client'

/**
 * Generate a clean username from a full name
 */
export function generateUsername(fullName: string): string {
  return fullName
    .toLowerCase()
    // Handle Turkish and other special characters
    .replace(/ğ/g, 'g')
    .replace(/ü/g, 'u')
    .replace(/ş/g, 's')
    .replace(/ı/g, 'i')
    .replace(/ö/g, 'o')
    .replace(/ç/g, 'c')
    // Handle other common special characters
    .replace(/[àáâãäå]/g, 'a')
    .replace(/[èéêë]/g, 'e')
    .replace(/[ìíîï]/g, 'i')
    .replace(/[òóôõö]/g, 'o')
    .replace(/[ùúûü]/g, 'u')
    .replace(/[ñ]/g, 'n')
    .replace(/[ç]/g, 'c')
    .replace(/[^a-z0-9\s-]/g, '') // Remove remaining special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50) // Limit to 50 characters
}

/**
 * Generate a unique username by checking database and adding numbers if needed
 */
export async function generateUniqueUsername(fullName: string): Promise<string> {
  const supabase = createClient()
  const baseUsername = generateUsername(fullName)
  
  // Check if base username is available
  const { data: existing } = await supabase
    .from('profiles')
    .select('username')
    .eq('username', baseUsername)
    .single()
  
  if (!existing) {
    return baseUsername
  }
  
  // If taken, try with numbers
  for (let i = 2; i <= 999; i++) {
    const candidateUsername = `${baseUsername}-${i}`
    
    const { data: existingNumbered } = await supabase
      .from('profiles')
      .select('username')
      .eq('username', candidateUsername)
      .single()
    
    if (!existingNumbered) {
      return candidateUsername
    }
  }
  
  // Fallback to timestamp if all numbers are taken
  return `${baseUsername}-${Date.now()}`
}

/**
 * Update a user's username
 */
export async function updateUsername(userId: string, newUsername: string): Promise<boolean> {
  const supabase = createClient()
  
  try {
    const { error } = await supabase
      .from('profiles')
      .update({ username: newUsername })
      .eq('id', userId)
    
    return !error
  } catch (error) {
    console.error('Error updating username:', error)
    return false
  }
}

/**
 * Check if a username is available
 */
export async function isUsernameAvailable(username: string): Promise<boolean> {
  const supabase = createClient()
  
  const { data } = await supabase
    .from('profiles')
    .select('username')
    .eq('username', username)
    .single()
  
  return !data
}
