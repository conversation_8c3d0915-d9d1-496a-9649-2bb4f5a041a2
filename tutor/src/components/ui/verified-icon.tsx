import React from 'react'

interface VerifiedIconProps {
  className?: string
  size?: number
}

export default function VerifiedIcon({ className = '', size = 16 }: VerifiedIconProps) {
  return (
    <div
      className={`inline-flex items-center justify-center ${className}`}
      style={{ width: size, height: size }}
      title="Verified Tutor"
    >
      <svg
        width={size}
        height={size}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Shield background with gradient */}
        <defs>
          <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#1f2937" />
            <stop offset="100%" stopColor="#374151" />
          </linearGradient>
        </defs>

        {/* Shield shape */}
        <path
          d="M12 2L4 6v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V6l-8-4z"
          fill="url(#shieldGradient)"
          stroke="#1f2937"
          strokeWidth="0.5"
        />

        {/* Checkmark */}
        <path
          d="M9 12l2 2 4-4"
          stroke="white"
          strokeWidth="2.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* Subtle highlight */}
        <path
          d="M12 2L4 6v2c0 1 0.2 2 0.5 3L12 4l7.5 7c0.3-1 0.5-2 0.5-3V6l-8-4z"
          fill="rgba(255,255,255,0.1)"
        />
      </svg>
    </div>
  )
}
