'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Home, LayoutDashboard, Shield, Users, ArrowLeft } from 'lucide-react'
import Logo from '@/components/logo'

interface AdminHeaderProps {
  title: string
  subtitle?: string
  showBackButton?: boolean
  backUrl?: string
  actions?: React.ReactNode
}

export default function AdminHeader({ 
  title, 
  subtitle, 
  showBackButton = true, 
  backUrl = '/',
  actions 
}: AdminHeaderProps) {
  return (
    <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between min-h-16 py-2 lg:py-0">
          {/* Top Row: Logo + Actions (Mobile/Tablet) */}
          <div className="flex items-center justify-between lg:justify-start lg:flex-1">
            {/* Left: Logo + Navigation */}
            <div className="flex items-center space-x-4 lg:space-x-6">
              <Link href="/" className="flex items-center space-x-2">
                <Logo variant="default" />
              </Link>

              {/* Admin Navigation - Hidden on mobile, visible on larger screens */}
              <nav className="hidden lg:flex items-center space-x-1">
                <Link href="/admin/dashboard">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                    <LayoutDashboard className="w-4 h-4 mr-2" />
                    Dashboard
                  </Button>
                </Link>
                <Link href="/admin">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                    <Shield className="w-4 h-4 mr-2" />
                    Approvals
                  </Button>
                </Link>
                <Link href="/intake">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900">
                    <Users className="w-4 h-4 mr-2" />
                    Intake
                  </Button>
                </Link>
              </nav>
            </div>

            {/* Right: Actions + Back Button */}
            <div className="flex items-center space-x-2 lg:space-x-3">
              {actions}

              {/* Mobile Back Button */}
              {showBackButton && (
                <Link href={backUrl} className="lg:hidden">
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="w-4 h-4" />
                  </Button>
                </Link>
              )}

              {/* Desktop Exit Admin */}
              <Link href="/" className="hidden lg:block">
                <Button variant="outline" size="sm" className="text-gray-600 hover:text-gray-900">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Exit Admin
                </Button>
              </Link>
            </div>
          </div>

          {/* Bottom Row: Title (Mobile/Tablet) or Inline (Desktop) */}
          <div className="mt-2 lg:mt-0 lg:flex-1 lg:text-left lg:ml-8">
            <h1 className="text-lg sm:text-xl font-semibold text-gray-900 truncate">{title}</h1>
            {subtitle && (
              <p className="text-xs sm:text-sm text-gray-500 truncate">{subtitle}</p>
            )}
          </div>
        </div>
      </div>
    </header>
  )
}
