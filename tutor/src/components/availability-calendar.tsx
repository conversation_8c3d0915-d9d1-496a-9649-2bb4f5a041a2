"use client"

import { useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { WeeklyAvailabilityRange, DEFAULT_TIME_SLOTS, generateNextNDates, getAvailableSlotsForDate, hasAnyAvailabilityForDate } from '@/lib/availability'

interface Props {
  tutorId: string
  onTimeSlotSelect?: (date: string, time: string) => void
  selectedSlots?: string[]
}

export default function AvailabilityCalendar({ tutorId, onTimeSlotSelect, selectedSlots = [] }: Props) {
  const supabase = createClient()
  const [weekly, setWeekly] = useState<WeeklyAvailabilityRange[]>([])
  const [dates, setDates] = useState<string[]>([])
  const [selectedDate, setSelectedDate] = useState<string>('')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const nextDates = generateNextNDates(14)
    setDates(nextDates)
    setSelectedDate(nextDates[0]) // Select first available date by default
  }, [])

  useEffect(() => {
    async function load() {
      setLoading(true)
      const { data, error } = await supabase
        .from('tutor_availability')
        .select('day_of_week, start_time, end_time, is_available')
        .eq('tutor_id', tutorId)

      if (!error && data) {
        setWeekly(data as WeeklyAvailabilityRange[])
      } else {
        setWeekly([])
      }
      setLoading(false)
    }
    if (tutorId) load()
  }, [tutorId])

  if (loading) return <div className="text-sm text-gray-500">Loading availability…</div>

  return (
    <div>
      <div className="grid grid-cols-7 text-center text-xs text-gray-500 mb-2">
        <span>Sun</span>
        <span>Mon</span>
        <span>Tue</span>
        <span>Wed</span>
        <span>Thu</span>
        <span>Fri</span>
        <span>Sat</span>
      </div>
      <div className="grid grid-cols-7 gap-1 text-center mb-4">
        {dates.map((d) => {
          const has = hasAnyAvailabilityForDate(d, weekly)
          const isSelected = selectedDate === d
          return (
            <button
              key={d}
              onClick={() => has && setSelectedDate(d)}
              disabled={!has}
              className={`p-2 text-sm rounded transition-colors ${
                isSelected
                  ? 'bg-blue-500 text-white'
                  : has
                    ? 'bg-green-100 text-green-800 hover:bg-green-200 cursor-pointer'
                    : 'text-gray-400 cursor-not-allowed'
              }`}
            >
              {new Date(d).getDate()}
            </button>
          )
        })}
      </div>

      {selectedDate && (
        <div>
          <h4 className="text-sm font-medium text-gray-800 mb-3">
            Available times for {new Date(selectedDate).toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric'
            })}
          </h4>
          <div className="grid grid-cols-3 gap-2">
            {DEFAULT_TIME_SLOTS.map((timeSlot) => {
              const available = getAvailableSlotsForDate(selectedDate, weekly).includes(timeSlot)
              const slotKey = `${selectedDate}-${timeSlot}`
              const isSelected = selectedSlots.includes(slotKey)

              return (
                <button
                  key={timeSlot}
                  onClick={() => available && onTimeSlotSelect?.(selectedDate, timeSlot)}
                  disabled={!available}
                  className={`py-2 px-3 text-sm rounded border transition-colors ${
                    isSelected
                      ? 'bg-blue-500 text-white border-blue-500'
                      : available
                        ? 'bg-green-100 text-green-800 border-green-300 hover:bg-green-200'
                        : 'bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed'
                  }`}
                >
                  {timeSlot}
                </button>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}

