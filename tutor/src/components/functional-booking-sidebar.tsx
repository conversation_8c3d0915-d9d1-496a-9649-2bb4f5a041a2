'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronLeft, ChevronRight, PlusCircle, Plus, Calendar, Clock, DollarSign } from 'lucide-react'
import AvailabilityCalendar from '@/components/availability-calendar'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'

interface FunctionalBookingSidebarProps {
  tutorId: string
  tutorName: string
  hourlyRate: number
  subjects: string[]
}

interface BookingSession {
  date: string
  time: string
  duration: number
  subject: string
}

export default function FunctionalBookingSidebar({ 
  tutorId, 
  tutorName, 
  hourlyRate, 
  subjects 
}: FunctionalBookingSidebarProps) {
  const router = useRouter()
  const supabase = createClient()
  
  const [selectedSessions, setSelectedSessions] = useState<BookingSession[]>([])
  const [selectedDuration, setSelectedDuration] = useState(60)
  const [selectedSubject, setSelectedSubject] = useState(subjects[0] || '')
  const [showDurationDropdown, setShowDurationDropdown] = useState(false)
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false)
  const [showMultipleSessions, setShowMultipleSessions] = useState(false)
  const [additionalSessions, setAdditionalSessions] = useState(0)
  const [loading, setLoading] = useState(false)
  const [user, setUser] = useState<any>(null)

  const durationOptions = [30, 60, 90, 120]
  const bundleDiscounts = {
    2: 0.05, // 5% for 2 sessions
    3: 0.10, // 10% for 3 sessions
    4: 0.15, // 15% for 4 sessions
    5: 0.20  // 20% for 5+ sessions
  }

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [supabase])

  const handleTimeSlotSelect = (date: string, time: string) => {
    const newSession: BookingSession = {
      date,
      time,
      duration: selectedDuration,
      subject: selectedSubject
    }

    // Check if this slot is already selected
    const existingIndex = selectedSessions.findIndex(
      session => session.date === date && session.time === time
    )

    if (existingIndex >= 0) {
      // Remove if already selected
      setSelectedSessions(prev => prev.filter((_, index) => index !== existingIndex))
    } else {
      // Add new session
      setSelectedSessions(prev => [...prev, newSession])
    }
  }

  const calculateTotal = () => {
    const totalSessions = selectedSessions.length + additionalSessions
    const basePrice = (hourlyRate / 100) * (selectedDuration / 60) * totalSessions
    
    let discount = 0
    if (totalSessions >= 5) discount = bundleDiscounts[5]
    else if (totalSessions >= 4) discount = bundleDiscounts[4]
    else if (totalSessions >= 3) discount = bundleDiscounts[3]
    else if (totalSessions >= 2) discount = bundleDiscounts[2]

    const discountAmount = basePrice * discount
    const finalPrice = basePrice - discountAmount

    return {
      basePrice,
      discount,
      discountAmount,
      finalPrice,
      totalSessions
    }
  }

  const handleBooking = async () => {
    if (!user) {
      router.push('/auth')
      return
    }

    if (selectedSessions.length === 0) {
      alert('Please select at least one time slot')
      return
    }

    setLoading(true)

    try {
      const { basePrice, finalPrice, totalSessions } = calculateTotal()

      // Create booking request
      const { data, error } = await supabase
        .from('booking_requests')
        .insert({
          student_id: user.id,
          tutor_id: tutorId,
          subject: selectedSubject,
          session_duration: selectedDuration,
          total_sessions: totalSessions,
          base_price: Math.round(basePrice * 100), // Convert to cents
          final_price: Math.round(finalPrice * 100), // Convert to cents
          selected_sessions: JSON.stringify(selectedSessions),
          additional_sessions: additionalSessions,
          status: 'pending',
          message: `Booking request for ${totalSessions} session(s) in ${selectedSubject}`
        })
        .select()
        .single()

      if (error) throw error

      // Redirect to booking success page
      router.push(`/bookings/${data.id}/success`)

    } catch (error) {
      console.error('Booking failed:', error)
      alert('Failed to create booking request. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const { basePrice, discount, discountAmount, finalPrice, totalSessions } = calculateTotal()

  return (
    <div className="w-full lg:w-1/3 bg-white rounded-xl shadow-lg p-6 h-fit sticky top-6">
      {/* Session Configuration */}
      <div className="mb-6">
        {/* Duration Selection */}
        <div className="relative mb-4">
          <button
            onClick={() => setShowDurationDropdown(!showDurationDropdown)}
            className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
          >
            <div className="flex items-center">
              <Clock className="w-4 h-4 mr-2 text-gray-600" />
              <span className="text-sm font-medium text-gray-700">
                ${((hourlyRate / 100) * (selectedDuration / 60)).toFixed(0)} / {selectedDuration} minutes
              </span>
            </div>
            <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${showDurationDropdown ? 'rotate-180' : ''}`} />
          </button>
          
          {showDurationDropdown && (
            <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
              {durationOptions.map(duration => (
                <button
                  key={duration}
                  onClick={() => {
                    setSelectedDuration(duration)
                    setShowDurationDropdown(false)
                  }}
                  className="w-full text-left px-3 py-2 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg"
                >
                  <span className="text-sm">
                    ${((hourlyRate / 100) * (duration / 60)).toFixed(0)} / {duration} minutes
                  </span>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Subject Selection */}
        {subjects.length > 1 && (
          <div className="relative mb-4">
            <button
              onClick={() => setShowSubjectDropdown(!showSubjectDropdown)}
              className="w-full flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
            >
              <div className="flex items-center">
                <DollarSign className="w-4 h-4 mr-2 text-gray-600" />
                <span className="text-sm font-medium text-gray-700">
                  Subject: {selectedSubject}
                </span>
              </div>
              <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${showSubjectDropdown ? 'rotate-180' : ''}`} />
            </button>
            
            {showSubjectDropdown && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                {subjects.map(subject => (
                  <button
                    key={subject}
                    onClick={() => {
                      setSelectedSubject(subject)
                      setShowSubjectDropdown(false)
                    }}
                    className="w-full text-left px-3 py-2 hover:bg-gray-50 first:rounded-t-lg last:rounded-b-lg"
                  >
                    <span className="text-sm">{subject}</span>
                  </button>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Selected Sessions Display */}
      {selectedSessions.length > 0 && (
        <div className="mb-6">
          <h4 className="text-sm font-medium text-gray-800 mb-3">Selected Sessions</h4>
          <div className="space-y-2">
            {selectedSessions.map((session, index) => (
              <div key={index} className="flex items-center justify-between p-2 bg-blue-50 rounded-lg">
                <div className="flex items-center text-sm">
                  <Calendar className="w-4 h-4 mr-2 text-blue-600" />
                  <span className="font-medium text-blue-800">{session.date}</span>
                  <span className="mx-2 text-blue-500">&bull;</span>
                  <span className="text-blue-600">{session.time}</span>
                </div>
                <button
                  onClick={() => setSelectedSessions(prev => prev.filter((_, i) => i !== index))}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  Remove
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Availability Calendar */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-800 mb-3">Select Available Times</h4>
        <AvailabilityCalendar 
          tutorId={tutorId} 
          onTimeSlotSelect={handleTimeSlotSelect}
          selectedSlots={selectedSessions.map(s => `${s.date}-${s.time}`)}
        />
      </div>

      {/* Add Multiple Sessions */}
      <div className="mb-6">
        <button
          onClick={() => setShowMultipleSessions(!showMultipleSessions)}
          className="flex justify-between items-center w-full mb-3 cursor-pointer"
        >
          <div className="flex items-center text-gray-800">
            <PlusCircle className="w-4 h-4 mr-2 text-gray-600" />
            <span className="font-medium text-sm">Add more sessions</span>
          </div>
          <ChevronDown className={`w-4 h-4 text-gray-400 transition-transform ${showMultipleSessions ? 'rotate-180' : ''}`} />
        </button>
        
        {showMultipleSessions && (
          <div>
            <p className="text-xs text-gray-500 mb-3">
              Purchase multiple sessions and save. You'll be able to select additional dates and times after booking.
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Additional sessions:</span>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setAdditionalSessions(Math.max(0, additionalSessions - 1))}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  -
                </button>
                <span className="w-8 text-center text-sm font-medium">{additionalSessions}</span>
                <button
                  onClick={() => setAdditionalSessions(additionalSessions + 1)}
                  className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                >
                  +
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Total and Checkout */}
      <div className="border-t border-gray-200 pt-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600 text-sm">
            {totalSessions} session{totalSessions !== 1 ? 's' : ''}
          </span>
          <span className="text-gray-800 font-medium">
            ${basePrice.toFixed(2)}
          </span>
        </div>
        
        {discount > 0 && (
          <>
            <div className="flex justify-between items-center mb-2">
              <span className="text-green-600 text-sm">
                Bundle discount ({Math.round(discount * 100)}%)
              </span>
              <span className="text-green-600 font-medium">
                -${discountAmount.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center mb-4">
              <span className="text-gray-800 font-semibold">Total (USD)</span>
              <div className="text-right">
                <div className="text-gray-400 line-through text-sm">${basePrice.toFixed(2)}</div>
                <div className="text-green-600 font-bold text-lg">${finalPrice.toFixed(2)}</div>
              </div>
            </div>
          </>
        )}
        
        {discount === 0 && (
          <div className="flex justify-between items-center mb-4">
            <span className="text-gray-800 font-semibold">Total (USD)</span>
            <div className="text-gray-800 font-bold text-lg">${finalPrice.toFixed(2)}</div>
          </div>
        )}

        <Button 
          onClick={handleBooking}
          disabled={loading || selectedSessions.length === 0}
          className="w-full bg-gray-800 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? 'Creating Request...' : selectedSessions.length === 0 ? 'Select Time Slots' : 'Request Booking'}
        </Button>
      </div>
    </div>
  )
}
