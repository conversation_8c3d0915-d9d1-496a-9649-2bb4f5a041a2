import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-black text-white py-16">
      <div className="max-w-7xl mx-auto px-4">
        {/* Top Section - Call to Action */}
        <div className="text-center mb-16">
          <p className="text-gray-300 text-sm mb-4">We&apos;re here to help.</p>
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Share your knowledge and<br />
            teach on your terms.
          </h2>
          <div className="flex justify-center space-x-4">
            <Link href="/auth">
              <button className="bg-white text-black px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                Get started
              </button>
            </Link>
            <Link href="/tutoring">
              <button className="border border-white text-white px-6 py-3 rounded-lg font-medium hover:bg-white hover:text-black transition-colors">
                Become a tutor
              </button>
            </Link>
          </div>
        </div>



        {/* Links Section */}
        <div className="grid md:grid-cols-5 gap-8 mb-12">
          {/* ProTutor */}
          <div>
            <h4 className="font-semibold mb-4">ProTutor</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li><Link href="/about" className="hover:text-white">About us</Link></li>
              <li><Link href="/careers" className="hover:text-white">Careers</Link></li>
              <li><Link href="/press" className="hover:text-white">Press</Link></li>
              <li><Link href="/blog" className="hover:text-white">Blog</Link></li>
              <li><Link href="/help" className="hover:text-white">Help & Support</Link></li>
              <li><Link href="/affiliate" className="hover:text-white">Affiliate program</Link></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="font-semibold mb-4">Support</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li><Link href="/help" className="hover:text-white">Help center</Link></li>
              <li><Link href="/contact" className="hover:text-white">Get in touch</Link></li>
              <li><Link href="/safety" className="hover:text-white">Trust and safety</Link></li>
              <li><Link href="/accessibility" className="hover:text-white">Accessibility</Link></li>
            </ul>
          </div>

          {/* Teaching */}
          <div>
            <h4 className="font-semibold mb-4">Teaching</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li><Link href="/tutoring" className="hover:text-white">Become a tutor</Link></li>
              <li><Link href="/teach-online" className="hover:text-white">Teach online</Link></li>
              <li><Link href="/tutor-resources" className="hover:text-white">Tutor resources</Link></li>
              <li><Link href="/community" className="hover:text-white">Tutor community</Link></li>
            </ul>
          </div>

          {/* Policies */}
          <div>
            <h4 className="font-semibold mb-4">Policies</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li><Link href="/terms" className="hover:text-white">Terms of service</Link></li>
              <li><Link href="/privacy" className="hover:text-white">Privacy policy</Link></li>
              <li><Link href="/cookie-policy" className="hover:text-white">Cookie policy</Link></li>
              <li><Link href="/community-guidelines" className="hover:text-white">Community guidelines</Link></li>
            </ul>
          </div>

          {/* More */}
          <div>
            <h4 className="font-semibold mb-4">More</h4>
            <ul className="space-y-2 text-sm text-gray-300">
              <li><Link href="/mobile" className="hover:text-white">Mobile app</Link></li>
              <li><Link href="/enterprise" className="hover:text-white">Enterprise</Link></li>
              <li><Link href="/gift-cards" className="hover:text-white">Gift cards</Link></li>
              <li><Link href="/referrals" className="hover:text-white">Referrals</Link></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-400 mb-4 md:mb-0">
            Copyright © 2024 ProTutor Inc. • Privacy • Terms
          </div>
          <div className="flex space-x-4">
            <Link href="#" className="text-gray-400 hover:text-white">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
              </svg>
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white">
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"/>
              </svg>
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
