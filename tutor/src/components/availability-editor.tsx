"use client"

import { useEffect, useMemo, useState } from "react"
import { createClient } from "@/lib/supabase/client"

const DAYS = ["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]

type DayState = {
  enabled: boolean
  start: string
  end: string
}

export default function AvailabilityEditor() {
  const supabase = createClient()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [userId, setUserId] = useState<string | null>(null)
  const [days, setDays] = useState<Record<number, DayState>>({
    0: { enabled: false, start: "09:00", end: "12:00" },
    1: { enabled: false, start: "09:00", end: "12:00" },
    2: { enabled: false, start: "09:00", end: "12:00" },
    3: { enabled: false, start: "09:00", end: "12:00" },
    4: { enabled: false, start: "09:00", end: "12:00" },
    5: { enabled: false, start: "09:00", end: "12:00" },
    6: { enabled: false, start: "09:00", end: "12:00" },
  })

  useEffect(() => {
    (async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        setLoading(false)
        return
      }
      setUserId(user.id)

      const { data, error } = await supabase
        .from("tutor_availability")
        .select("day_of_week, start_time, end_time, is_available")
        .eq("tutor_id", user.id)

      if (!error && data) {
        const next = { ...days }
        for (const row of data as any[]) {
          const d = row.day_of_week as number
          next[d] = {
            enabled: !!row.is_available,
            start: (row.start_time as string).slice(0,5),
            end: (row.end_time as string).slice(0,5)
          }
        }
        setDays(next)
      }
      setLoading(false)
    })()
  }, [])

  const canSave = useMemo(() => !!userId, [userId])

  const updateDay = (idx: number, patch: Partial<DayState>) => {
    setDays(prev => ({ ...prev, [idx]: { ...prev[idx], ...patch } }))
  }

  const onSave = async () => {
    if (!userId) return
    setSaving(true)
    try {
      // Clear and insert fresh (simpler UX)
      const { error: delErr } = await supabase
        .from("tutor_availability")
        .delete()
        .eq("tutor_id", userId)
      if (delErr) throw delErr

      const rows = Object.entries(days)
        .filter(([_, v]) => v.enabled)
        .map(([k, v]) => ({
          tutor_id: userId,
          day_of_week: Number(k),
          start_time: v.start + ":00",
          end_time: v.end + ":00",
          is_available: true
        }))

      if (rows.length) {
        const { error: insErr } = await supabase
          .from("tutor_availability")
          .insert(rows)
        if (insErr) throw insErr
      }
      alert("Availability saved.")
    } catch (e) {
      console.error(e)
      alert("Failed to save availability. Please try again.")
    } finally {
      setSaving(false)
    }
  }

  if (loading) return <div className="text-sm text-gray-500">Loading…</div>
  if (!userId) return <div className="text-sm text-gray-500">Sign in to manage your availability.</div>

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Weekly Availability</h3>
      <p className="text-sm text-gray-600">Students will see available days in green and only the time slots you enable.</p>
      <div className="space-y-3">
        {DAYS.map((label, idx) => {
          const st = days[idx]
          return (
            <div key={idx} className="flex items-center gap-3">
              <label className="w-28 text-sm text-gray-700">{label}</label>
              <input
                type="checkbox"
                checked={st.enabled}
                onChange={e => updateDay(idx, { enabled: e.target.checked })}
              />
              <input
                type="time"
                value={st.start}
                onChange={e => updateDay(idx, { start: e.target.value })}
                className="border rounded px-2 py-1 text-sm"
                disabled={!st.enabled}
              />
              <span className="text-gray-500 text-sm">to</span>
              <input
                type="time"
                value={st.end}
                onChange={e => updateDay(idx, { end: e.target.value })}
                className="border rounded px-2 py-1 text-sm"
                disabled={!st.enabled}
              />
            </div>
          )
        })}
      </div>
      <button
        onClick={onSave}
        disabled={!canSave || saving}
        className="bg-black text-white px-4 py-2 rounded disabled:opacity-50"
      >
        {saving ? "Saving…" : "Save Availability"}
      </button>
    </div>
  )
}

