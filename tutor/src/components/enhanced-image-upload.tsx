'use client'

import { useState, useRef, useCallback } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Upload, X, User, Crop as CropIcon, RotateCw, ZoomIn, ZoomOut } from 'lucide-react'
import ReactCrop, { Crop, PixelCrop } from 'react-image-crop'
import 'react-image-crop/dist/ReactCrop.css'

interface EnhancedImageUploadProps {
  currentImageUrl?: string | null
  onImageChange: (imageUrl: string | null) => void
  userId: string
  disabled?: boolean
  size?: 'small' | 'medium' | 'large'
  aspectRatio?: number // e.g., 1 for square, 4/3 for rectangle
  cropShape?: 'rect' | 'round'
}

export default function EnhancedImageUpload({
  currentImageUrl,
  onImageChange,
  userId,
  disabled = false,
  size = 'medium',
  aspectRatio = 1,
  cropShape = 'rect'
}: EnhancedImageUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentImageUrl || null)
  const [showCropModal, setShowCropModal] = useState(false)
  const [originalImage, setOriginalImage] = useState<string | null>(null)
  const [crop, setCrop] = useState<Crop>()
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>()
  const [rotation, setRotation] = useState(0)
  const [scale, setScale] = useState(1)
  
  const fileInputRef = useRef<HTMLInputElement>(null)
  const imgRef = useRef<HTMLImageElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const sizeClasses = {
    small: 'w-20 h-20',
    medium: 'w-32 h-32',
    large: 'w-48 h-48'
  }

  // Supported image formats
  const supportedFormats = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/webp',
    'image/gif'
  ]

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type with expanded support
    if (!supportedFormats.includes(file.type)) {
      alert('Please select a supported image file (JPEG, PNG, WebP, GIF)')
      return
    }

    // Validate file size (max 10MB for better quality)
    if (file.size > 10 * 1024 * 1024) {
      alert('Image must be less than 10MB')
      return
    }

    // Create preview and show crop modal
    const reader = new FileReader()
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string
      setOriginalImage(imageUrl)
      setShowCropModal(true)
      
      // Initialize crop to center square/rectangle
      const initialCrop: Crop = {
        unit: '%',
        x: 25,
        y: 25,
        width: 50,
        height: cropShape === 'round' ? 50 : 50 / aspectRatio
      }
      setCrop(initialCrop)
    }
    reader.readAsDataURL(file)
  }

  const onImageLoad = useCallback((e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget
    
    // Set initial crop based on aspect ratio
    const cropWidth = Math.min(width, height) * 0.6
    const cropHeight = cropShape === 'round' ? cropWidth : cropWidth / aspectRatio
    
    const crop: Crop = {
      unit: 'px',
      x: (width - cropWidth) / 2,
      y: (height - cropHeight) / 2,
      width: cropWidth,
      height: cropHeight
    }
    
    setCrop(crop)
  }, [aspectRatio, cropShape])

  const getCroppedImg = useCallback(async (): Promise<Blob | null> => {
    if (!completedCrop || !imgRef.current || !canvasRef.current) {
      return null
    }

    const image = imgRef.current
    const canvas = canvasRef.current
    const ctx = canvas.getContext('2d')

    if (!ctx) {
      return null
    }

    const scaleX = image.naturalWidth / image.width
    const scaleY = image.naturalHeight / image.height

    // Set canvas size to crop size
    canvas.width = completedCrop.width
    canvas.height = completedCrop.height

    // Apply transformations
    ctx.save()
    
    // Handle rotation
    if (rotation !== 0) {
      ctx.translate(canvas.width / 2, canvas.height / 2)
      ctx.rotate((rotation * Math.PI) / 180)
      ctx.translate(-canvas.width / 2, -canvas.height / 2)
    }

    // Draw the cropped image
    ctx.drawImage(
      image,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width,
      completedCrop.height
    )

    ctx.restore()

    return new Promise((resolve) => {
      canvas.toBlob(resolve, 'image/jpeg', 0.9)
    })
  }, [completedCrop, rotation])

  const handleCropConfirm = async () => {
    try {
      setUploading(true)
      
      const croppedImageBlob = await getCroppedImg()
      if (!croppedImageBlob) {
        throw new Error('Failed to crop image')
      }

      // Upload cropped image
      const formData = new FormData()
      formData.append('file', croppedImageBlob, 'cropped-image.jpg')
      formData.append('type', 'profile')

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const { url } = await response.json()

      // Update parent component
      onImageChange(url)
      setPreviewUrl(url)
      setShowCropModal(false)
      setOriginalImage(null)
      
    } catch (error) {
      console.error('Upload failed:', error)
      alert('Failed to upload image. Please try again.')
    } finally {
      setUploading(false)
    }
  }

  const handleCropCancel = () => {
    setShowCropModal(false)
    setOriginalImage(null)
    setCrop(undefined)
    setCompletedCrop(undefined)
    setRotation(0)
    setScale(1)
    
    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleRemoveImage = () => {
    setPreviewUrl(null)
    onImageChange(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  const rotateImage = () => {
    setRotation((prev) => (prev + 90) % 360)
  }

  const adjustScale = (delta: number) => {
    setScale((prev) => Math.max(0.5, Math.min(3, prev + delta)))
  }

  return (
    <div className="space-y-4">
      <Label htmlFor="image-upload" className="text-sm font-medium">
        Profile Picture
      </Label>
      
      {/* Preview */}
      <div className={`${sizeClasses[size]} mx-auto relative group`}>
        {previewUrl ? (
          <div className="relative w-full h-full">
            <img
              src={previewUrl}
              alt="Profile preview"
              className={`w-full h-full object-cover ${cropShape === 'round' ? 'rounded-full' : 'rounded-lg'} border-2 border-gray-200`}
            />
            {!disabled && (
              <button
                onClick={handleRemoveImage}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                title="Remove image"
              >
                <X className="w-3 h-3" />
              </button>
            )}
          </div>
        ) : (
          <div className={`w-full h-full ${cropShape === 'round' ? 'rounded-full' : 'rounded-lg'} border-2 border-dashed border-gray-300 flex items-center justify-center bg-gray-50`}>
            <User className="w-8 h-8 text-gray-400" />
          </div>
        )}
      </div>

      {/* Upload Button */}
      {!disabled && (
        <div className="flex justify-center">
          <Button
            type="button"
            variant="outline"
            onClick={handleButtonClick}
            disabled={uploading}
            className="flex items-center space-x-2"
          >
            <Upload className="w-4 h-4" />
            <span>{uploading ? 'Processing...' : previewUrl ? 'Change Image' : 'Upload Image'}</span>
          </Button>
        </div>
      )}

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={supportedFormats.join(',')}
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Crop Modal */}
      {showCropModal && originalImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 max-w-4xl max-h-[90vh] overflow-auto">
            <h3 className="text-lg font-semibold mb-4">Crop & Adjust Your Image</h3>
            
            {/* Crop Controls */}
            <div className="flex items-center space-x-4 mb-4">
              <Button
                variant="outline"
                size="sm"
                onClick={rotateImage}
                className="flex items-center space-x-1"
              >
                <RotateCw className="w-4 h-4" />
                <span>Rotate</span>
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => adjustScale(-0.1)}
                className="flex items-center space-x-1"
              >
                <ZoomOut className="w-4 h-4" />
              </Button>
              
              <span className="text-sm text-gray-600">
                Scale: {Math.round(scale * 100)}%
              </span>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => adjustScale(0.1)}
                className="flex items-center space-x-1"
              >
                <ZoomIn className="w-4 h-4" />
              </Button>
            </div>

            {/* Crop Area */}
            <div className="mb-4 max-w-full overflow-auto">
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={cropShape === 'round' ? 1 : aspectRatio}
                circularCrop={cropShape === 'round'}
              >
                <img
                  ref={imgRef}
                  alt="Crop preview"
                  src={originalImage}
                  style={{ 
                    transform: `scale(${scale}) rotate(${rotation}deg)`,
                    maxWidth: '100%',
                    maxHeight: '60vh'
                  }}
                  onLoad={onImageLoad}
                />
              </ReactCrop>
            </div>

            {/* Hidden canvas for processing */}
            <canvas
              ref={canvasRef}
              style={{ display: 'none' }}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3">
              <Button
                variant="outline"
                onClick={handleCropCancel}
                disabled={uploading}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCropConfirm}
                disabled={uploading || !completedCrop}
                className="flex items-center space-x-2"
              >
                <CropIcon className="w-4 h-4" />
                <span>{uploading ? 'Uploading...' : 'Crop & Upload'}</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
