'use client'

import { useState, useRef, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { User } from '@supabase/supabase-js'
import Link from 'next/link'
import { LogOut, Settings, User as UserIcon, Shield, Crown, ChevronDown, Calendar } from 'lucide-react'

interface ProfileDropdownProps {
  user: User
  className?: string
}

export default function ProfileDropdown({ user, className = '' }: ProfileDropdownProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isHover, setIsHover] = useState(false)
  const [profile, setProfile] = useState<any>(null)
  const dropdownRef = useRef<HTMLDivElement>(null)
  const supabase = createClient()

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Fetch user profile
  useEffect(() => {
    async function fetchProfile() {
      if (user) {
        const { data } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()
        setProfile(data)
      }
    }
    fetchProfile()
  }, [user, supabase])

  const handleSignOut = async () => {
    await supabase.auth.signOut()
    window.location.reload()
  }

  const displayName = profile?.full_name || user.email?.split('@')[0] || 'User'
  const initials = displayName.split(' ').map((n: string) => n[0]).join('').toUpperCase().slice(0, 2)
  const avatarUrl: string | null = profile?.profile_picture_url || profile?.avatar_url || null

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Profile Circle Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
        onKeyDown={(e) => { if (e.key === 'Enter' || e.key === ' ') { e.preventDefault(); setIsOpen((v) => !v) } }}
        className="group relative cursor-pointer w-10 h-10 bg-black rounded-full flex items-center justify-center transition-all duration-200 focus:outline-none ring-0 hover:ring-2 hover:ring-gray-900/30 hover:shadow-md"
        style={{ perspective: '800px' }}
        aria-haspopup="menu"
        aria-expanded={isOpen}
        aria-controls="profile-menu"
        title="Open profile menu"
      >
        {avatarUrl ? (
          <img
            src={avatarUrl}
            alt={displayName}
            className="w-10 h-10 rounded-full object-cover transform-gpu transition-transform duration-200"
            style={{ transform: isHover ? 'translateZ(16px) scale(1.06)' : 'translateZ(0) scale(1)' }}
          />
        ) : (
          <span
            className="text-white text-sm font-medium transform-gpu transition-transform duration-200"
            style={{ transform: isHover ? 'translateZ(16px) scale(1.06)' : 'translateZ(0) scale(1)' }}
          >
            {initials}
          </span>
        )}
        {/* Hover indicator chevron badge */}
        <div className="pointer-events-none absolute -right-1 -bottom-1 w-4 h-4 rounded-full bg-white text-black flex items-center justify-center shadow-md opacity-0 group-hover:opacity-100 transition-opacity">
          <ChevronDown className="w-3 h-3" />
        </div>
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div id="profile-menu" className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
          {/* User Info Header */}
          <div className="px-4 py-3 border-b border-gray-100">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center">
                {avatarUrl ? (
                  <img
                    src={avatarUrl}
                    alt={displayName}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-white text-base font-medium">
                    {initials}
                  </span>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {displayName}
                </p>
                <p className="text-sm text-gray-500 truncate">
                  {user.email}
                </p>
              </div>
            </div>
          </div>

          {/* Menu Items */}
          <div className="py-1">
            <Link
              href="/dashboard"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              onClick={() => setIsOpen(false)}
            >
              <UserIcon className="w-4 h-4 mr-3 text-gray-400" />
              Dashboard
            </Link>

            <Link
              href="/profile"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="w-4 h-4 mr-3 text-gray-400" />
              Account
            </Link>

            <Link
              href="/roles"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              onClick={() => setIsOpen(false)}
            >
              <Shield className="w-4 h-4 mr-3 text-gray-400" />
              Manage Roles
            </Link>

            {/* Bookings link for tutors */}
            {profile?.roles?.includes('tutor') && profile?.tutor_status === 'approved' && (
              <Link
                href="/bookings"
                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                onClick={() => setIsOpen(false)}
              >
                <Calendar className="w-4 h-4 mr-3 text-gray-400" />
                Tutor Bookings
              </Link>
            )}

            {/* Student bookings link - available to all users */}
            <Link
              href="/my-bookings"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              onClick={() => setIsOpen(false)}
            >
              <Calendar className="w-4 h-4 mr-3 text-gray-400" />
              My Booking Requests
            </Link>

            {/* Admin Links - Only show if user has admin role */}
            {profile?.roles?.includes('admin') && profile?.admin_status === 'approved' && (
              <>
                <Link
                  href="/admin/dashboard"
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                  onClick={() => setIsOpen(false)}
                >
                  <Crown className="w-4 h-4 mr-3 text-gray-400" />
                  Admin Dashboard
                </Link>
                <Link
                  href="/admin"
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                  onClick={() => setIsOpen(false)}
                >
                  <Shield className="w-4 h-4 mr-3 text-gray-400" />
                  Tutor Approvals
                </Link>
                <Link
                  href="/intake"
                  className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
                  onClick={() => setIsOpen(false)}
                >
                  <UserIcon className="w-4 h-4 mr-3 text-gray-400" />
                  Intake Console
                </Link>
              </>
            )}

            <Link
              href="/settings"
              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
              onClick={() => setIsOpen(false)}
            >
              <Settings className="w-4 h-4 mr-3 text-gray-400" />
              Settings
            </Link>
          </div>

          {/* Divider */}
          <div className="border-t border-gray-100 my-1"></div>

          {/* Sign Out */}
          <button
            onClick={handleSignOut}
            className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-150"
          >
            <LogOut className="w-4 h-4 mr-3 text-gray-400" />
            Sign out
          </button>
        </div>
      )}
    </div>
  )
}
