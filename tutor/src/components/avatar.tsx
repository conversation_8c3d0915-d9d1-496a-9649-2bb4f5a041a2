'use client'

import { useState } from 'react'

interface AvatarProps {
  src?: string | null
  name: string
  id: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
  showUploadOnHover?: boolean
  onUploadClick?: () => void
  showOnlineStatus?: boolean
  isOnline?: boolean
}

const sizeClasses = {
  sm: 'w-12 h-12 text-sm',
  md: 'w-16 h-16 text-base',
  lg: 'w-20 h-20 text-lg',
  xl: 'w-28 h-28 text-xl'
}

export default function Avatar({
  src,
  name,
  id,
  size = 'md',
  className = '',
  showUploadOnHover = false,
  onUploadClick,
  showOnlineStatus = false,
  isOnline = true
}: AvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // Generate fallback avatar URL
  const generateFallbackAvatar = (name: string, id: string) => {
    const colors = ['FF6B6B', '4ECDC4', '45B7D1', '96CEB4', 'FFEAA7', 'DDA0DD', 'FFB6C1', '87CEEB']
    const hash = (name + id).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    const color = colors[Math.abs(hash) % colors.length]
    const sizePixels = size === 'sm' ? 48 : size === 'md' ? 64 : size === 'lg' ? 80 : 112
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&size=${sizePixels}&background=${color}&color=fff&bold=true`
  }

  // Determine which image to show
  const shouldShowRealImage = src && !imageError && !src.includes('ui-avatars.com')
  const imageUrl = shouldShowRealImage ? src : generateFallbackAvatar(name, id)

  const handleImageError = () => {
    setImageError(true)
  }

  const handleUploadClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onUploadClick?.()
  }

  return (
    <div 
      className={`relative ${sizeClasses[size]} ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main avatar image */}
      <img
        src={imageUrl}
        alt={name}
        className={`${sizeClasses[size]} rounded-full object-cover border-2 border-gray-200`}
        onError={handleImageError}
      />

      {/* Upload overlay on hover */}
      {showUploadOnHover && isHovered && onUploadClick && (
        <div 
          className={`
            absolute inset-0 ${sizeClasses[size]} rounded-full 
            bg-black bg-opacity-50 flex items-center justify-center 
            cursor-pointer transition-opacity duration-200
          `}
          onClick={handleUploadClick}
        >
          <svg 
            className="w-4 h-4 text-white" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" 
            />
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
        </div>
      )}

      {/* Online Status Indicator */}
      {showOnlineStatus && (
        <div className={`absolute bottom-0 right-0 w-3 h-3 rounded-full border-2 border-white transform translate-x-1 translate-y-1 ${
          isOnline ? 'bg-green-500' : 'bg-gray-400'
        }`} />
      )}

      {/* Upload indicator for real images */}
      {shouldShowRealImage && !showOnlineStatus && (
        <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center">
          <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  )
}
