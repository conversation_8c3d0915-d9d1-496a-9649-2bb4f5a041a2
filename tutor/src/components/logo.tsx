'use client'

import Link from 'next/link'

interface LogoProps {
  variant?: 'default' | 'white' | 'minimal' | 'icon-only'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  href?: string
}

export default function Logo({ 
  variant = 'default', 
  size = 'md', 
  className = '',
  href = '/'
}: LogoProps) {
  
  const sizeClasses = {
    sm: {
      container: 'h-8',
      icon: 'w-6 h-6',
      text: 'text-base font-bold',
      spacing: 'space-x-2'
    },
    md: {
      container: 'h-10',
      icon: 'w-8 h-8',
      text: 'text-lg font-extrabold',
      spacing: 'space-x-2'
    },
    lg: {
      container: 'h-12',
      icon: 'w-10 h-10',
      text: 'text-xl font-extrabold',
      spacing: 'space-x-3'
    }
  }

  const colors = {
    default: {
      iconBg: 'bg-black',
      iconElements: 'text-white',
      text: 'text-gray-900',
      accent: 'text-black'
    },
    white: {
      iconBg: 'bg-white border border-gray-200',
      iconElements: 'text-black',
      text: 'text-white',
      accent: 'text-white'
    },
    minimal: {
      iconBg: 'bg-gray-100',
      iconElements: 'text-gray-700',
      text: 'text-gray-900',
      accent: 'text-gray-700'
    }
  }

  const currentSize = sizeClasses[size]
  const currentColors = colors[variant === 'icon-only' ? 'default' : variant]

  // Modern geometric icon representing learning/growth/connection
  const LogoIcon = () => (
    <div className={`${currentSize.icon} ${currentColors.iconBg} rounded-xl flex items-center justify-center relative overflow-hidden`}>
      {/* Main geometric pattern - represents knowledge/connection */}
      <svg 
        viewBox="0 0 24 24" 
        className={`w-5 h-5 ${currentColors.iconElements}`}
        fill="none"
      >
        {/* Learning path/growth curve */}
        <path 
          d="M3 17L9 11L13 15L21 7" 
          stroke="currentColor" 
          strokeWidth="2.5" 
          strokeLinecap="round" 
          strokeLinejoin="round"
        />
        {/* Knowledge nodes */}
        <circle cx="3" cy="17" r="2" fill="currentColor" />
        <circle cx="9" cy="11" r="2" fill="currentColor" />
        <circle cx="13" cy="15" r="2" fill="currentColor" />
        <circle cx="21" cy="7" r="2" fill="currentColor" />
        {/* Connection lines (subtle) */}
        <path 
          d="M5 15L7 13M11 13L11 13M15 13L19 9" 
          stroke="currentColor" 
          strokeWidth="1" 
          strokeLinecap="round" 
          opacity="0.6"
        />
      </svg>
      
      {/* Clean black and white design - no overlay needed */}
    </div>
  )

  // Alternative icon - more abstract/modern (unused for now)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const LogoIconAlt = () => (
    <div className={`${currentSize.icon} ${currentColors.iconBg} rounded-xl flex items-center justify-center relative overflow-hidden`}>
      <svg 
        viewBox="0 0 24 24" 
        className={`w-4 h-4 ${currentColors.iconElements}`}
        fill="currentColor"
      >
        {/* Abstract P shape with modern twist */}
        <path d="M6 2v20h3V14h6c3.31 0 6-2.69 6-6s-2.69-6-6-6H6zm3 3h6c1.66 0 3 1.34 3 3s-1.34 3-3 3H9V5z" />
        {/* Modern accent dot */}
        <circle cx="18" cy="18" r="2" />
      </svg>
      
      {/* Clean black and white design */}
    </div>
  )

  const LogoContent = () => (
    <div className={`flex items-center ${currentSize.spacing} ${className}`}>
      <LogoIcon />
      {variant !== 'icon-only' && (
        <div className="flex items-center space-x-1">
          <span className={`${currentSize.text} ${currentColors.text} tracking-tight`}>
            Pro
          </span>
          <span className={`${currentSize.text} ${currentColors.accent} tracking-tight`}>
            Tutor
          </span>
        </div>
      )}
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="flex items-center hover:opacity-80 transition-opacity">
        <LogoContent />
      </Link>
    )
  }

  return <LogoContent />
}

// Export alternative logo variations
export function LogoMinimal(props: Omit<LogoProps, 'variant'>) {
  return <Logo {...props} variant="minimal" />
}

export function LogoWhite(props: Omit<LogoProps, 'variant'>) {
  return <Logo {...props} variant="white" />
}

export function LogoIcon(props: Omit<LogoProps, 'variant'>) {
  return <Logo {...props} variant="icon-only" />
}

// Alternative logo with different icon style
export function LogoAlt({ 
  variant = 'default', 
  size = 'md', 
  className = '',
  href = '/'
}: LogoProps) {
  
  const sizeClasses = {
    sm: { icon: 'w-6 h-6', text: 'text-base font-bold' },
    md: { icon: 'w-8 h-8', text: 'text-lg font-extrabold' },
    lg: { icon: 'w-10 h-10', text: 'text-xl font-extrabold' }
  }

  const colors = {
    default: {
      iconBg: 'bg-black',
      iconElements: 'text-white',
      text: 'text-gray-900'
    },
    white: {
      iconBg: 'bg-white border border-gray-200',
      iconElements: 'text-black',
      text: 'text-white'
    },
    minimal: {
      iconBg: 'bg-gray-100',
      iconElements: 'text-gray-800',
      text: 'text-gray-900'
    }
  }

  const currentSize = sizeClasses[size]
  const currentColors = colors[variant === 'icon-only' ? 'default' : variant]

  const LogoContent = () => (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Framer-style geometric logo */}
      <div className={`${currentSize.icon} ${currentColors.iconBg} rounded-lg flex items-center justify-center`}>
        <svg 
          viewBox="0 0 16 16" 
          className={`w-4 h-4 ${currentColors.iconElements}`}
          fill="currentColor"
        >
          {/* Modern geometric P */}
          <path d="M2 1v14h2V9h4c2.21 0 4-1.79 4-4s-1.79-4-4-4H2zm2 2h4c1.1 0 2 .9 2 2s-.9 2-2 2H4V3z" />
          {/* Accent element */}
          <rect x="11" y="11" width="3" height="3" rx="1" />
        </svg>
      </div>
      {variant !== 'icon-only' && (
        <span className={`${currentSize.text} ${currentColors.text} tracking-tight`}>
          ProTutor
        </span>
      )}
    </div>
  )

  if (href) {
    return (
      <Link href={href} className="flex items-center hover:opacity-80 transition-opacity">
        <LogoContent />
      </Link>
    )
  }

  return <LogoContent />
}
