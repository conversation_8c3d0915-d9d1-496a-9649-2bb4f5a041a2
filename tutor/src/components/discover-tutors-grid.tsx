'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { Star, Clock, MessageCircle } from 'lucide-react'
import VerifiedIcon from '@/components/ui/verified-icon'

interface Tutor {
  id: string
  username: string | null
  full_name: string
  bio: string
  subjects: string[]
  hourly_rate: number
  tutor_tier: string
  average_rating: number | null
  total_sessions_taught: number
  profile_picture_url: string | null
}

export default function DiscoverTutorsGrid() {
  const [tutors, setTutors] = useState<Tutor[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    fetchDiscoverTutors()
  }, [])

  const fetchDiscoverTutors = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, username, full_name, bio, subjects, hourly_rate, tutor_tier, average_rating, total_sessions_taught, profile_picture_url')
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .not('hourly_rate', 'is', null)
        .order('created_at', { ascending: false })
        .limit(12)

      if (error) throw error
      setTutors(data || [])
    } catch (error) {
      console.error('Error fetching discover tutors:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatRate = (rate: number) => {
    return `$${(rate / 100).toFixed(0)}/hr`
  }

  const formatSubjects = (subjects: string[]) => {
    if (!subjects || subjects.length === 0) return 'General Tutoring'
    return subjects.slice(0, 2).map(s => s.replace('_', ' ')).join(', ') + 
           (subjects.length > 2 ? ` +${subjects.length - 2} more` : '')
  }

  const generateMockRating = (id: string) => {
    // Generate consistent mock rating based on ID for tutors without ratings
    const hash = id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    return 4.0 + (Math.abs(hash) % 100) / 100
  }

  const generateMockReviews = (id: string) => {
    const hash = id.split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    return 15 + (Math.abs(hash) % 85) // 15-100 reviews
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
  }

  if (loading) {
    return (
      <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {[...Array(12)].map((_, i) => (
          <div key={i} className="bg-white border border-gray-200 rounded-lg p-6 animate-pulse">
            <div className="flex items-start space-x-4">
              <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {tutors.map((tutor) => {
        const rating = tutor.average_rating || generateMockRating(tutor.id)
        const reviews = tutor.total_sessions_taught || generateMockReviews(tutor.id)
        
        return (
          <Link key={tutor.id} href={`/${tutor.id}`}>
            <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200 cursor-pointer h-full">
              {/* Header with Avatar and Basic Info */}
              <div className="flex items-start space-x-4 mb-4">
                <div className="w-12 h-12 bg-black rounded-full flex items-center justify-center flex-shrink-0 transition-transform duration-200 hover:scale-105 ring-0 hover:ring-2 hover:ring-gray-900/40">
                  {tutor.profile_picture_url ? (
                    <img
                      src={tutor.profile_picture_url}
                      alt={tutor.full_name}
                      className="w-12 h-12 rounded-full object-cover transition-transform duration-200"
                    />
                  ) : (
                    <span className="text-white text-sm font-medium">
                      {getInitials(tutor.full_name)}
                    </span>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center mb-1">
                    <h3 className="font-semibold text-gray-900 truncate mr-2">{tutor.full_name}</h3>
                    {tutor.tutor_tier === 'verified' && (
                      <VerifiedIcon size={16} className="ml-1 flex-shrink-0" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <span className="font-medium">{formatRate(tutor.hourly_rate)}</span>
                  </div>
                </div>
              </div>

              {/* Stats Row */}
              <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="font-medium">{rating.toFixed(1)}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <MessageCircle className="w-4 h-4" />
                  <span>{reviews} reviews</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>~2 min</span>
                </div>
              </div>

              {/* Subjects */}
              <div className="mb-3">
                <p className="text-sm font-medium text-gray-700">
                  {formatSubjects(tutor.subjects)}
                </p>
              </div>

              {/* Bio Preview */}
              <p className="text-sm text-gray-600 line-clamp-2">
                {tutor.bio || 'Experienced tutor ready to help you achieve your academic goals.'}
              </p>
            </div>
          </Link>
        )
      })}
    </div>
  )
}
