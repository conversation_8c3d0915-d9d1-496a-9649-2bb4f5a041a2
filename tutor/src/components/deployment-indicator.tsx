'use client'

import { useState, useEffect } from 'react'
import { Git<PERSON>ommit, Clock, CheckCircle, Rocket } from 'lucide-react'
import { getDeploymentInfo, isNewDeployment } from '@/lib/deployment-info'

interface DeploymentInfo {
  version: string
  timestamp: string
  commit: string
  status: 'deployed' | 'deploying' | 'error'
  buildNumber?: number
}

export default function DeploymentIndicator() {
  const [deploymentInfo, setDeploymentInfo] = useState<DeploymentInfo>({
    version: 'v1.2.4',
    timestamp: new Date().toISOString(),
    commit: '1ec45b9',
    status: 'deployed',
    buildNumber: 24
  })
  const [isVisible, setIsVisible] = useState(false)
  const [isNewDeploy, setIsNewDeploy] = useState(false)

  useEffect(() => {
    // Load deployment info
    const info = getDeploymentInfo()
    setDeploymentInfo({
      version: info.version,
      timestamp: info.timestamp,
      commit: info.commit,
      status: info.status,
      buildNumber: info.buildNumber
    })

    // Check if this is a new deployment
    const isNew = isNewDeployment()
    setIsNewDeploy(isNew)

    // Auto-show for 4 seconds if new deployment
    if (isNew) {
      setIsVisible(true)
      const timer = setTimeout(() => setIsVisible(false), 4000)
      return () => clearTimeout(timer)
    }
  }, [])

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'deployed': return 'bg-green-500'
      case 'deploying': return 'bg-yellow-500'
      case 'error': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'deployed': return <CheckCircle className="w-3 h-3" />
      case 'deploying': return <Clock className="w-3 h-3 animate-spin" />
      case 'error': return <GitCommit className="w-3 h-3" />
      default: return <GitCommit className="w-3 h-3" />
    }
  }

  return (
    <>
      {/* Floating Indicator - Always visible in bottom right */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="relative">
          <button
            onClick={() => setIsVisible(!isVisible)}
            className={`w-10 h-10 rounded-full ${getStatusColor(deploymentInfo.status)} text-white shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center group ${isNewDeploy ? 'animate-pulse' : ''}`}
            title={`Deployment ${deploymentInfo.status} - Click for details`}
          >
            {isNewDeploy ? <Rocket className="w-4 h-4" /> : getStatusIcon(deploymentInfo.status)}
          </button>

          {/* New deployment badge */}
          {isNewDeploy && (
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
          )}
        </div>
      </div>

      {/* Detailed Info Panel */}
      {isVisible && (
        <div className="fixed bottom-16 right-4 z-50 bg-white rounded-lg shadow-xl border border-gray-200 p-4 min-w-[280px] animate-in slide-in-from-bottom-2 duration-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900 flex items-center">
              <GitCommit className="w-4 h-4 mr-2" />
              Deployment Status
            </h3>
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-gray-600 text-sm"
            >
              ✕
            </button>
          </div>
          
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Status:</span>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${getStatusColor(deploymentInfo.status)}`}></div>
                <span className="font-medium capitalize">{deploymentInfo.status}</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-gray-600">Version:</span>
              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                {deploymentInfo.version}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Build:</span>
              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                #{deploymentInfo.buildNumber}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Commit:</span>
              <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                {deploymentInfo.commit}
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-gray-600">Deployed:</span>
              <span className="text-xs">{formatTime(deploymentInfo.timestamp)}</span>
            </div>
          </div>

          {deploymentInfo.status === 'deployed' && isNewDeploy && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center text-xs text-green-600">
                <Rocket className="w-3 h-3 mr-1" />
                🎉 New deployment is live!
              </div>
            </div>
          )}

          {deploymentInfo.status === 'deployed' && !isNewDeploy && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center text-xs text-green-600">
                <CheckCircle className="w-3 h-3 mr-1" />
                System is running smoothly
              </div>
            </div>
          )}

          {deploymentInfo.status === 'deploying' && (
            <div className="mt-3 pt-3 border-t border-gray-100">
              <div className="flex items-center text-xs text-yellow-600">
                <Clock className="w-3 h-3 mr-1 animate-spin" />
                Deployment in progress...
              </div>
            </div>
          )}
        </div>
      )}
    </>
  )
}

// Hook to update deployment info (can be called from anywhere)
export function useDeploymentStatus() {
  const updateDeployment = (info: Partial<DeploymentInfo>) => {
    // In a real app, this would update global state or trigger a re-render
    // For now, we'll use localStorage to persist across page loads
    const current = JSON.parse(localStorage.getItem('deploymentInfo') || '{}')
    const updated = { ...current, ...info }
    localStorage.setItem('deploymentInfo', JSON.stringify(updated))
    
    // Trigger a custom event to update the component
    window.dispatchEvent(new CustomEvent('deploymentUpdate', { detail: updated }))
  }

  return { updateDeployment }
}
