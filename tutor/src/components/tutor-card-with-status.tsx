'use client'

import { useOnlineStatus } from '@/hooks/useOnlineStatus'
import RectangularAvatar from '@/components/rectangular-avatar'
import { Clock, Star, CheckCircle } from 'lucide-react'

interface TutorCardWithStatusProps {
  tutor: {
    id: string
    full_name: string
    profile_picture_url?: string | null
    bio?: string | null
    subjects?: string[] | null
    hourly_rate?: number | null
    experience_years?: number | null
    response_time_hours?: number | null
    total_sessions_taught?: number | null
    is_verified?: boolean | null
  }
  onClick: () => void
}

export default function TutorCardWithStatus({ tutor, onClick }: TutorCardWithStatusProps) {
  const { isOnline } = useOnlineStatus(tutor.id)

  return (
    <div 
      className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-200 cursor-pointer hover:border-gray-300 group"
      onClick={onClick}
    >
      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          <RectangularAvatar
            src={tutor.profile_picture_url}
            name={tutor.full_name}
            id={tutor.id}
            className="w-20 h-20"
            showOnlineStatus={true}
            isOnline={isOnline}
          />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-2">
            <h3 className="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
              {tutor.full_name}
            </h3>
            {tutor.is_verified && (
              <CheckCircle className="w-5 h-5 text-blue-500 flex-shrink-0" />
            )}
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
            {tutor.response_time_hours && (
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{tutor.response_time_hours}h response</span>
              </div>
            )}
            
            {tutor.total_sessions_taught && (
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4" />
                <span>{tutor.total_sessions_taught} sessions</span>
              </div>
            )}
            
            {tutor.hourly_rate && (
              <div className="font-medium text-gray-900">
                ${(tutor.hourly_rate / 100).toFixed(0)}/hr
              </div>
            )}
          </div>
          
          {tutor.bio && (
            <p className="text-gray-700 text-sm line-clamp-2 mb-3">
              {tutor.bio}
            </p>
          )}
          
          {tutor.subjects && tutor.subjects.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {tutor.subjects.slice(0, 3).map((subject, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {subject}
                </span>
              ))}
              {tutor.subjects.length > 3 && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                  +{tutor.subjects.length - 3} more
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
