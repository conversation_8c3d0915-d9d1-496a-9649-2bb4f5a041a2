'use client'

import { useState, useRef } from 'react'
import { createClient } from '@/lib/supabase/client'
import { validateImageFile } from '@/lib/filen'

interface ProfilePictureUploadProps {
  currentAvatarUrl?: string
  userId: string
  onUploadSuccess: (newAvatarUrl: string) => void
  className?: string
}

export default function ProfilePictureUpload({
  userId,
  onUploadSuccess,
  className = ''
}: Omit<ProfilePictureUploadProps, 'currentAvatarUrl'>) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const supabase = createClient() // Will be used when we add client-side features

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file
    const validation = validateImageFile(file)
    if (!validation.valid) {
      setError(validation.error!)
      return
    }

    setError(null)
    setUploading(true)
    setUploadProgress(0)

    try {
      // Create form data for API upload
      const formData = new FormData()
      formData.append('file', file)
      formData.append('userId', userId)

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90))
      }, 300)

      // Upload to server API (which uploads to Filen)
      const response = await fetch('/api/upload-profile-picture', {
        method: 'POST',
        body: formData,
      })

      // Clear progress interval
      clearInterval(progressInterval)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()
      setUploadProgress(100)

      // Success callback with the Filen URL
      onUploadSuccess(result.avatarUrl)
      
      // Reset form
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
      
    } catch (error) {
      console.error('Upload failed:', error)
      setError(error instanceof Error ? error.message : 'Upload failed')
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={`relative ${className}`}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/png,image/webp"
        onChange={handleFileSelect}
        className="hidden"
        disabled={uploading}
      />

      {/* Upload button */}
      <button
        onClick={triggerFileSelect}
        disabled={uploading}
        className={`
          relative overflow-hidden rounded-lg border-2 border-dashed border-gray-300 
          hover:border-gray-400 focus:border-gray-500 focus:outline-none
          transition-colors duration-200 p-4 w-full
          ${uploading ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:bg-gray-50'}
        `}
      >
        {uploading ? (
          <div className="flex flex-col items-center space-y-2">
            {/* Progress bar */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
            <span className="text-sm text-gray-600">
              Uploading... {uploadProgress}%
            </span>
          </div>
        ) : (
          <div className="flex flex-col items-center space-y-2">
            {/* Upload icon */}
            <svg 
              className="w-8 h-8 text-gray-400" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" 
              />
            </svg>
            <div className="text-center">
              <p className="text-sm font-medium text-gray-900">
                Upload Profile Picture
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG, WebP up to 5MB
              </p>
            </div>
          </div>
        )}
      </button>

      {/* Error message */}
      {error && (
        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-600">
          {error}
        </div>
      )}

      {/* Success message */}
      {uploadProgress === 100 && !error && (
        <div className="mt-2 p-2 bg-green-50 border border-green-200 rounded text-sm text-green-600">
          Profile picture updated successfully!
        </div>
      )}
    </div>
  )
}
