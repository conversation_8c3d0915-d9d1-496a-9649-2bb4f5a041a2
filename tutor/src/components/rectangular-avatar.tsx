'use client'

import { useState } from 'react'

interface RectangularAvatarProps {
  src?: string | null
  name: string
  id: string
  className?: string
  showUploadOnHover?: boolean
  onUploadClick?: () => void
  popOnHover?: boolean
  showOnlineStatus?: boolean
  isOnline?: boolean
}

export default function RectangularAvatar({
  src,
  name,
  id,
  className = '',
  showUploadOnHover = false,
  onUploadClick,
  popOnHover = false,
  showOnlineStatus = false,
  isOnline = true,
}: RectangularAvatarProps) {
  const [imageError, setImageError] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // Generate initials for fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2)
  }

  // Generate background color based on name and id
  const getBackgroundColor = (name: string, id: string) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ]
    const hash = (name + id).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0)
      return a & a
    }, 0)
    return colors[Math.abs(hash) % colors.length]
  }

  // Determine which image to show
  const shouldShowRealImage = src && !imageError && !src.includes('ui-avatars.com')

  const handleImageError = () => {
    setImageError(true)
  }

  const handleUploadClick = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    onUploadClick?.()
  }

  return (
    <div
      className={`relative ${className}`}
      style={popOnHover ? { perspective: '900px' } : undefined}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {shouldShowRealImage ? (
        <img
          src={src}
          alt={name}
          className={`w-full h-full object-cover rounded-2xl transform-gpu transition-transform duration-200 ${isHovered && popOnHover ? 'shadow-xl' : ''} ${className}`}
          style={popOnHover ? { transform: isHovered ? 'translateZ(22px) scale(1.035)' : 'translateZ(0) scale(1)' } : undefined}
          onError={handleImageError}
        />
      ) : (
        <div className={`w-full h-full rounded-2xl ${getBackgroundColor(name, id)} flex items-center justify-center transform-gpu transition-transform duration-200 ${isHovered && popOnHover ? 'shadow-xl' : ''} ${className}`}
          style={popOnHover ? { transform: isHovered ? 'translateZ(22px) scale(1.035)' : 'translateZ(0) scale(1)' } : undefined}
        >
          <span className="text-white text-4xl font-bold">
            {getInitials(name)}
          </span>
        </div>
      )}

      {/* Upload overlay on hover */}
      {showUploadOnHover && isHovered && onUploadClick && (
        <div 
          className={`
            absolute inset-0 rounded-2xl 
            bg-black bg-opacity-50 flex items-center justify-center 
            cursor-pointer transition-opacity duration-200
          `}
          onClick={handleUploadClick}
        >
          <svg 
            className="w-8 h-8 text-white" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" 
            />
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" 
            />
          </svg>
        </div>
      )}

      {/* Online Status Indicator */}
      {showOnlineStatus && (
        <div className={`absolute bottom-2 right-2 w-4 h-4 rounded-full border-2 border-white ${
          isOnline ? 'bg-green-500' : 'bg-gray-400'
        }`} />
      )}

      {/* Upload indicator for real images */}
      {shouldShowRealImage && !showOnlineStatus && (
        <div className="absolute -bottom-2 -right-2 w-6 h-6 bg-blue-500 border-2 border-white rounded-full flex items-center justify-center">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )}
    </div>
  )
}
