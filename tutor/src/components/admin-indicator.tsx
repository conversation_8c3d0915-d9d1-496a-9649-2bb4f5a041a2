'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { User } from '@supabase/supabase-js'
import { Shield, Crown } from 'lucide-react'

interface AdminIndicatorProps {
  user: User
}

interface Profile {
  roles?: string[]
  admin_status?: string
}

export default function AdminIndicator({ user }: AdminIndicatorProps) {
  const [profile, setProfile] = useState<Profile | null>(null)
  const supabase = createClient()

  useEffect(() => {
    const fetchProfile = async () => {
      const { data } = await supabase
        .from('profiles')
        .select('roles, admin_status')
        .eq('id', user.id)
        .single()
      
      setProfile(data)
    }

    fetchProfile()
  }, [user.id, supabase])

  if (!profile?.roles) return null

  const isAdmin = profile.roles.includes('admin') && profile.admin_status === 'approved'
  const isIntake = profile.roles.includes('intake')

  if (!isAdmin && !isIntake) return null

  return (
    <div className="flex items-center">
      {isAdmin && (
        <div className="flex items-center space-x-1 px-2 py-1 bg-red-50 text-red-700 rounded-full text-xs font-medium border border-red-200">
          <Crown className="w-3 h-3" />
          <span>Admin</span>
        </div>
      )}
      {isIntake && !isAdmin && (
        <div className="flex items-center space-x-1 px-2 py-1 bg-blue-50 text-blue-700 rounded-full text-xs font-medium border border-blue-200">
          <Shield className="w-3 h-3" />
          <span>Staff</span>
        </div>
      )}
    </div>
  )
}
