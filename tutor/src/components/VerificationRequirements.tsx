'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertCircle, ExternalLink } from 'lucide-react'
import Link from 'next/link'

interface Profile {
  id: string
  roles: string[]
  tutor_tier: 'standard' | 'verified'
  tutor_status: 'pending' | 'approved' | 'rejected'
  education?: string
  experience_years?: number
  certifications?: string[]
  bio?: string
  subjects?: string[]
  hourly_rate?: number
}

interface VerificationCriteria {
  hasEducation: boolean
  hasUniversityEducation: boolean
  hasExperience: boolean
  hasCertifications: boolean
  hasBio: boolean
  hasSubjects: boolean
  hasHourlyRate: boolean
  isComplete: boolean
}

export default function VerificationRequirements({ userId }: { userId: string }) {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [criteria, setCriteria] = useState<VerificationCriteria | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    loadProfile()
  }, [userId])

  const loadProfile = async () => {
    try {
      const { data } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()
      
      if (data) {
        setProfile(data)
        evaluateCriteria(data)
      }
    } catch (error) {
      console.error('Error loading profile:', error)
    } finally {
      setLoading(false)
    }
  }

  const evaluateCriteria = (profile: Profile) => {
    const hasEducation = !!profile.education
    const hasUniversityEducation = hasEducation && 
      /university|college|bachelor|master|phd|doctorate/i.test(profile.education!)
    const hasExperience = (profile.experience_years || 0) >= 2
    const hasCertifications = !!(profile.certifications && profile.certifications.length > 0)
    const hasBio = !!profile.bio
    const hasSubjects = !!(profile.subjects && profile.subjects.length > 0)
    const hasHourlyRate = !!(profile.hourly_rate && profile.hourly_rate >= 1500) // At least $15/hour

    const isComplete = hasUniversityEducation && hasExperience && hasCertifications && hasBio && hasSubjects && hasHourlyRate

    setCriteria({
      hasEducation,
      hasUniversityEducation,
      hasExperience,
      hasCertifications,
      hasBio,
      hasSubjects,
      hasHourlyRate,
      isComplete
    })
  }

  const RequirementItem = ({ 
    met, 
    title, 
    description, 
    actionText, 
    actionHref 
  }: {
    met: boolean
    title: string
    description: string
    actionText?: string
    actionHref?: string
  }) => (
    <div className="flex items-start gap-3 p-3 rounded-lg border bg-card">
      {met ? (
        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
      ) : (
        <XCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
      )}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-medium text-sm">{title}</h4>
          <Badge variant={met ? "default" : "destructive"} className="text-xs">
            {met ? "Complete" : "Required"}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground mb-2">{description}</p>
        {!met && actionText && actionHref && (
          <Link href={actionHref}>
            <Button size="sm" variant="outline" className="text-xs h-7">
              {actionText}
              <ExternalLink className="h-3 w-3 ml-1" />
            </Button>
          </Link>
        )}
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded mb-4"></div>
        <div className="space-y-3">
          {[1,2,3,4].map(i => (
            <div key={i} className="h-16 bg-gray-100 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  if (!profile || !profile.roles.includes('tutor')) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              {profile.tutor_tier === 'verified' ? (
                <>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Verified Tutor
                </>
              ) : (
                <>
                  <AlertCircle className="h-5 w-5 text-orange-500" />
                  Standard Tutor
                </>
              )}
            </CardTitle>
            <CardDescription>
              {profile.tutor_tier === 'verified' 
                ? "You're verified and can charge premium rates!"
                : "Complete these requirements to become a verified tutor"
              }
            </CardDescription>
          </div>
          <Badge 
            variant={profile.tutor_tier === 'verified' ? "default" : "secondary"}
            className="text-sm"
          >
            {profile.tutor_tier === 'verified' ? '✓ Verified' : 'Standard'}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {criteria && (
          <>
            {/* Progress Summary */}
            <div className="bg-muted/50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Verification Progress</span>
                <span className="text-sm text-muted-foreground">
                  {[criteria.hasUniversityEducation, criteria.hasExperience, criteria.hasCertifications, criteria.hasBio, criteria.hasSubjects].filter(Boolean).length}/5 completed
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                  style={{ 
                    width: `${([criteria.hasUniversityEducation, criteria.hasExperience, criteria.hasCertifications, criteria.hasBio, criteria.hasSubjects].filter(Boolean).length / 5) * 100}%` 
                  }}
                ></div>
              </div>
            </div>

            {/* Requirements List */}
            <div className="space-y-3">
              <RequirementItem
                met={criteria.hasUniversityEducation}
                title="University Education"
                description="Include university, college, bachelor's, master's, or PhD in your education field"
                actionText="Update Education"
                actionHref="/tutor/profile"
              />
              
              <RequirementItem
                met={criteria.hasExperience}
                title="Teaching Experience (2+ years)"
                description={`You have ${profile.experience_years || 0} years listed. Need at least 2 years.`}
                actionText="Update Experience"
                actionHref="/tutor/profile"
              />
              
              <RequirementItem
                met={criteria.hasCertifications}
                title="Certifications"
                description={`You have ${profile.certifications?.length || 0} certifications. Need at least 1.`}
                actionText="Add Certifications"
                actionHref="/tutor/profile"
              />
              
              <RequirementItem
                met={criteria.hasBio}
                title="Professional Bio"
                description="Write a detailed bio describing your teaching background and approach"
                actionText="Write Bio"
                actionHref="/tutor/profile"
              />
              
              <RequirementItem
                met={criteria.hasSubjects}
                title="Subject Specialization"
                description={`You teach ${profile.subjects?.length || 0} subjects. Add your areas of expertise.`}
                actionText="Add Subjects"
                actionHref="/tutor/profile"
              />

              <RequirementItem
                met={criteria.hasHourlyRate}
                title="Hourly Rate"
                description={`Set your hourly rate. Current: ${profile.hourly_rate ? `$${(profile.hourly_rate / 100).toFixed(0)}/hr` : 'Not set'}`}
                actionText="Set Rate"
                actionHref="/tutor/profile"
              />
            </div>

            {/* Action Section */}
            {profile.tutor_tier === 'verified' ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  <h4 className="font-medium text-green-800">Verification Complete!</h4>
                </div>
                <p className="text-sm text-green-700 mb-3">
                  You&apos;re verified and can now charge premium rates and access advanced features!
                </p>
                <div className="bg-green-100 border border-green-300 rounded p-3">
                  <h5 className="text-xs text-green-700 font-medium mb-1">✅ Verified Benefits Active</h5>
                  <ul className="text-xs text-green-600 space-y-1">
                    <li>• Premium pricing tier unlocked</li>
                    <li>• Priority placement in search results</li>
                    <li>• Verified badge displayed on profile</li>
                  </ul>
                </div>
              </div>
            ) : criteria.isComplete ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                  <h4 className="font-medium text-blue-800">Ready for Verification!</h4>
                </div>
                <p className="text-sm text-blue-700 mb-3">
                  You meet all verification requirements. Our admin team will review your profile and approve verification manually.
                </p>
                <div className="bg-blue-100 border border-blue-300 rounded p-3 mt-3">
                  <p className="text-xs text-blue-700 font-medium mb-1">⏳ Pending Admin Review</p>
                  <p className="text-xs text-blue-600">
                    Your profile is complete and ready for verification. An admin will review and approve your verified status.
                  </p>
                </div>
              </div>
            ) : (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-800 mb-2">Benefits of Verification</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Higher hourly rates (premium pricing tier)</li>
                  <li>• Priority placement in search results</li>
                  <li>• Verified badge on your profile</li>
                  <li>• Access to advanced features</li>
                  <li>• Increased student trust and bookings</li>
                </ul>
                <Link href="/tutor/profile">
                  <Button className="mt-3" size="sm">
                    Complete Your Profile
                    <ExternalLink className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
                <p className="text-xs text-blue-600 mt-3">
                  Complete your profile to be eligible for manual verification by our admin team.
                </p>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}