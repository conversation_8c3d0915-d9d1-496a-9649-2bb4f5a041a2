'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function TutorsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to the new search page
    router.replace('/search')
  }, [router])

  return (
    <div className="page-container flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to search page...</p>
      </div>
    </div>
  )
}
