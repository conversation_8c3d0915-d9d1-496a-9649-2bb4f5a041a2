"use client"

import { useEffect, useMemo, useRef, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { ChevronDown, ChevronUp } from "lucide-react"
import AdminHeader from "@/components/admin-header"
import Avatar from "@/components/avatar"

// Student intake form structure (stored as JSON in lead_student_details.preferences)
type StudentForm = {
  meeting_date: string
  meeting_time: string
  meeting_teacher: string
  student_full_name: string
  school_grade: string
  math_course: 'IB (AA)' | 'AP Calculus AB' | 'AP Calculus BC' | ''
  ib_level: 'HL' | 'SL' | ''
  guardian_name: string
  guardian_phone: string
  feedback_frequency: 'after_each' | 'weekly' | 'monthly' | ''
  tuition_fee: string
  payment_frequency: 'weekly' | 'monthly' | 'per_lesson' | 'other' | ''
  payment_other: string
  target_grade: 4 | 5 | 6 | 7 | null
  university_goal: string
  primary_goals: {
    fundamentals: boolean
    exam_strategies: boolean
    time_management: boolean
    ia_guidance: boolean
    gdc: boolean
    other: string
  }
  topics_challenging: string
  topics_strong: string
  ib_topics: { algebra: boolean; functions: boolean; trigonometry: boolean; calculus: boolean; probability: boolean; statistics: boolean }
  ap_topics: { limits: boolean; derivatives: boolean; applications_of_derivatives: boolean; integrals: boolean; applications_of_integrals: boolean; differential_equations: boolean }
  upcoming_assessments: string
  expected_difficulty: 1 | 2 | 3 | 4 | 5 | null
  special_prep: string
  learning_method: 'visual' | 'auditory' | 'kinesthetic' | 'reading_writing' | ''
  group_or_individual: string
  lesson_support_areas: string
  tech_tools: string
  want_tech_support: boolean
  extra_resources: string
  math_interest: 'very_high' | 'moderate' | 'low' | ''
  strengths_outside_math: string
}

const defaultStudentForm = (): StudentForm => ({
  meeting_date: '',
  meeting_time: '',
  meeting_teacher: '',
  student_full_name: '',
  school_grade: '',
  math_course: '',
  ib_level: '',
  guardian_name: '',
  guardian_phone: '',
  feedback_frequency: '',
  tuition_fee: '',
  payment_frequency: '',
  payment_other: '',
  target_grade: null,
  university_goal: '',
  primary_goals: { fundamentals: false, exam_strategies: false, time_management: false, ia_guidance: false, gdc: false, other: '' },
  topics_challenging: '',
  topics_strong: '',
  ib_topics: { algebra: false, functions: false, trigonometry: false, calculus: false, probability: false, statistics: false },
  ap_topics: { limits: false, derivatives: false, applications_of_derivatives: false, integrals: false, applications_of_integrals: false, differential_equations: false },
  upcoming_assessments: '',
  expected_difficulty: null,
  special_prep: '',
  learning_method: '',
  group_or_individual: '',
  lesson_support_areas: '',
  tech_tools: '',
  want_tech_support: false,
  extra_resources: '',
  math_interest: '',
  strengths_outside_math: ''
})

// Minimal types
type Lead = {
  id: string
  lead_type: "student" | "tutor"
  status: "new" | "qualified" | "proposal_sent" | "waiting" | "won" | "lost"
  source: string
  created_by: string | null
  assigned_to: string | null
  linked_profile_id: string | null
  primary_contact_name: string | null
  primary_contact_email: string | null
  primary_contact_phone: string | null
  notes: string | null
  created_at: string
  updated_at: string
}

// User profile type for intake management
type UserProfile = {
  id: string
  email: string
  full_name: string
  roles: string[]
  student_status: string | null
  tutor_status: string | null
  admin_status: string | null
  phone_number: string | null
  intake_status: 'incomplete' | 'complete' | 'locked'
  intake_completed_at: string | null
  intake_completed_by: string | null
  intake_notes: string | null
  profile_picture_url: string | null
  avatar_url: string | null
  created_at: string
  updated_at: string
}

// Combined item type for unified list
type IntakeItem = {
  id: string
  type: 'lead' | 'user'
  name: string
  email: string | null
  phone: string | null
  user_type: 'student' | 'tutor' | 'admin'
  status: string
  intake_status?: 'incomplete' | 'complete' | 'locked'
  created_at: string
  data: Lead | UserProfile
}

const COUNTRY_CODES = ["+1","+44","+49","+90","+971","+33","+34","+61"] as const

function Section({ title, defaultOpen=true, children }: { title: string; defaultOpen?: boolean; children: React.ReactNode }){
  const [open, setOpen] = useState(defaultOpen)
  return (
    <div className="border rounded-md">
      <button type="button" onClick={()=>setOpen(o=>!o)} className="w-full flex items-center justify-between px-3 py-2 text-left hover:bg-accent/30">
        <span className="text-sm font-semibold">{title}</span>
        {open ? <ChevronUp className="h-4 w-4"/> : <ChevronDown className="h-4 w-4"/>}
      </button>
      {open && <div className="p-3 space-y-3">{children}</div>}
    </div>
  )
}

type CountryCode = typeof COUNTRY_CODES[number]

export default function IntakeConsolePage() {
  const router = useRouter()
  const supabase = createClient()

  // State management
  const [loading, setLoading] = useState(true)
  const [authUser, setAuthUser] = useState<any>(null)
  const [profile, setProfile] = useState<any>(null)

  // Data arrays
  const [leads, setLeads] = useState<Lead[]>([])
  const [users, setUsers] = useState<UserProfile[]>([])
  const [intakeItems, setIntakeItems] = useState<IntakeItem[]>([])

  // UI state
  const [selectedItem, setSelectedItem] = useState<IntakeItem | null>(null)
  const [statusFilter, setStatusFilter] = useState<string>("incomplete")
  const [saving, setSaving] = useState(false)

  // Mobile navigation state
  const [mobileView, setMobileView] = useState<'list' | 'detail'>('list')

  // Form state for editing
  const [editingLead, setEditingLead] = useState<Lead | null>(null)
  const [editingUser, setEditingUser] = useState<UserProfile | null>(null)

  // Load leads from database
  const loadLeads = async () => {
    try {
      const { data, error } = await supabase
        .from('leads')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setLeads(data || [])
    } catch (error) {
      console.error('Error loading leads:', error)
    }
  }

  // Load users with intake status
  const loadUsers = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select(`
          id, email, full_name, roles,
          student_status, tutor_status, admin_status,
          phone_number, profile_picture_url, avatar_url,
          intake_status, intake_completed_at, intake_completed_by, intake_notes,
          created_at, updated_at
        `)
        .order('created_at', { ascending: false })

      if (error) throw error
      setUsers(data || [])
    } catch (error) {
      console.error('Error loading users:', error)
    }
  }

  // Initialize data loading
  useEffect(() => {
    const initializeData = async () => {
      setLoading(true)

      // Get authenticated user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth')
        return
      }
      setAuthUser(user)

      // Get user profile
      const { data: profileData } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileData) {
        setProfile(profileData)
      }

      // Load data
      await Promise.all([loadLeads(), loadUsers()])
      setLoading(false)
    }

    initializeData()
  }, [])

  // Combine leads and users into unified intake items
  useEffect(() => {
    const combined: IntakeItem[] = []

    // Add leads
    leads.forEach(lead => {
      combined.push({
        id: lead.id,
        type: 'lead',
        name: lead.primary_contact_name || 'Unnamed Lead',
        email: lead.primary_contact_email,
        phone: lead.primary_contact_phone,
        user_type: lead.lead_type as 'student' | 'tutor',
        status: lead.status,
        created_at: lead.created_at,
        data: lead
      })
    })

    // Add users
    users.forEach(user => {
      const userType = user.roles.includes('tutor') ? 'tutor' :
                      user.roles.includes('admin') ? 'admin' : 'student'

      combined.push({
        id: user.id,
        type: 'user',
        name: user.full_name,
        email: user.email,
        phone: user.phone_number,
        user_type: userType,
        status: user.student_status || user.tutor_status || user.admin_status || 'active',
        intake_status: user.intake_status,
        created_at: user.created_at,
        data: user
      })
    })

    // Sort by intake status (incomplete first) and then by date
    combined.sort((a, b) => {
      if (a.intake_status === 'incomplete' && b.intake_status !== 'incomplete') return -1
      if (a.intake_status !== 'incomplete' && b.intake_status === 'incomplete') return 1
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    })

    setIntakeItems(combined)
  }, [leads, users])

  // Select an item for editing
  const selectItem = (item: IntakeItem) => {
    setSelectedItem(item)
    // Switch to detail view on mobile when item is selected
    setMobileView('detail')
    if (item.type === 'lead') {
      setEditingLead(item.data as Lead)
      setEditingUser(null)
    } else {
      setEditingUser(item.data as UserProfile)
      setEditingLead(null)
    }
  }

  // Save lead changes
  const saveLead = async () => {
    if (!editingLead) return
    setSaving(true)

    try {
      const { error } = await supabase
        .from('leads')
        .update({
          primary_contact_name: editingLead.primary_contact_name,
          primary_contact_email: editingLead.primary_contact_email,
          primary_contact_phone: editingLead.primary_contact_phone,
          notes: editingLead.notes,
          status: editingLead.status
        })
        .eq('id', editingLead.id)

      if (error) throw error

      await loadLeads()
      alert('Lead saved successfully')
    } catch (error) {
      console.error('Error saving lead:', error)
      alert('Failed to save lead')
    } finally {
      setSaving(false)
    }
  }

  // Save user profile changes
  const saveUser = async () => {
    if (!editingUser) return
    setSaving(true)

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: editingUser.full_name,
          phone_number: editingUser.phone_number
        })
        .eq('id', editingUser.id)

      if (error) throw error

      await loadUsers()
      alert('User profile saved successfully')
    } catch (error) {
      console.error('Error saving user:', error)
      alert('Failed to save user profile')
    } finally {
      setSaving(false)
    }
  }

  // Complete intake
  const completeIntake = async () => {
    if (!editingUser || !profile) return
    setSaving(true)

    try {
      const { error } = await supabase.rpc('complete_intake', {
        p_profile_id: editingUser.id,
        p_completed_by: profile.id,
        p_notes: 'Completed by ' + profile.full_name + ' on ' + new Date().toISOString()
      })

      if (error) throw error

      await loadUsers()
      alert('Intake marked as complete')
    } catch (error) {
      console.error('Error completing intake:', error)
      alert('Failed to complete intake')
    } finally {
      setSaving(false)
    }
  }

  // Unlock intake for editing
  const unlockIntake = async () => {
    if (!editingUser || !profile) return
    setSaving(true)

    try {
      const { error } = await supabase.rpc('unlock_intake', {
        p_profile_id: editingUser.id,
        p_unlocked_by: profile.id
      })

      if (error) throw error

      await loadUsers()
      alert('Intake unlocked for editing')
    } catch (error) {
      console.error('Error unlocking intake:', error)
      alert('Failed to unlock intake')
    } finally {
      setSaving(false)
    }
  }

  // Lock intake
  const lockIntake = async () => {
    if (!editingUser || !profile) return
    setSaving(true)

    try {
      const { error } = await supabase.rpc('lock_intake', {
        p_profile_id: editingUser.id,
        p_locked_by: profile.id,
        p_notes: 'Locked by ' + profile.full_name + ' on ' + new Date().toISOString()
      })

      if (error) throw error

      await loadUsers()
      alert('Intake locked')
    } catch (error) {
      console.error('Error locking intake:', error)
      alert('Failed to lock intake')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading intake console...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <AdminHeader
        title="Intake Console"
        subtitle="Process leads and manage user intake completion"
        actions={
          <div className="flex gap-2">
            {selectedItem?.type === 'lead' && (
              <Button onClick={saveLead} disabled={saving} size="sm">
                {saving ? "Saving..." : "Save Lead"}
              </Button>
            )}
            {selectedItem?.type === 'user' && editingUser && (
              <>
                <Button onClick={saveUser} disabled={saving} size="sm">
                  {saving ? "Saving..." : "Save User"}
                </Button>
                {editingUser.intake_status === 'incomplete' && (
                  <Button onClick={completeIntake} disabled={saving} size="sm" className="bg-green-600 hover:bg-green-700">
                    Mark Complete
                  </Button>
                )}
                {editingUser.intake_status === 'complete' && (
                  <Button onClick={unlockIntake} disabled={saving} variant="outline" size="sm">
                    Unlock for Edit
                  </Button>
                )}
                {editingUser.intake_status === 'locked' && (
                  <Button onClick={unlockIntake} disabled={saving} variant="outline" size="sm">
                    Unlock
                  </Button>
                )}
                {editingUser.intake_status === 'complete' && (
                  <Button onClick={lockIntake} disabled={saving} variant="destructive" size="sm">
                    Lock
                  </Button>
                )}
              </>
            )}
            <Button variant="outline" onClick={() => { loadLeads(); loadUsers(); }} size="sm">
              Refresh
            </Button>
          </div>
        }
      />

      {/* Mobile Tab Navigation */}
      <div className="md:hidden border-b bg-white">
        <div className="flex">
          <button
            onClick={() => setMobileView('list')}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              mobileView === 'list'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            }`}
          >
            Items ({intakeItems.filter(item => statusFilter === "all" || item.intake_status === "incomplete" || item.type === "lead").length})
          </button>
          <button
            onClick={() => setMobileView('detail')}
            disabled={!selectedItem}
            className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
              mobileView === 'detail' && selectedItem
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
            } ${!selectedItem ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            {selectedItem ? `Details: ${selectedItem.name}` : 'Select an item'}
          </button>
        </div>
      </div>

      <main className="flex-1 grid grid-cols-12">
        {/* Left Sidebar - Item List */}
        <aside className={`${mobileView === 'list' ? 'col-span-12' : 'hidden'} md:col-span-4 lg:col-span-3 md:block border-r border-gray-200 bg-white p-3 space-y-2`}>
          <div className="grid grid-cols-2 gap-1 text-xs">
            <Button
              size="sm"
              variant={statusFilter === "incomplete" ? "default" : "outline"}
              onClick={() => setStatusFilter("incomplete")}
            >
              Incomplete
            </Button>
            <Button
              size="sm"
              variant={statusFilter === "all" ? "default" : "outline"}
              onClick={() => setStatusFilter("all")}
            >
              All
            </Button>
          </div>

          <div className="space-y-1 max-h-[calc(100vh-200px)] overflow-y-auto">
            {intakeItems
              .filter(item => statusFilter === "all" || item.intake_status === "incomplete" || item.type === "lead")
              .map((item) => (
                <div
                  key={item.id}
                  onClick={() => selectItem(item)}
                  className={`p-2 rounded cursor-pointer border text-sm ${
                    selectedItem?.id === item.id
                      ? 'bg-blue-50 border-blue-200'
                      : 'hover:bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Avatar
                        src={item.type === 'user' ? (item.data as UserProfile).profile_picture_url || (item.data as UserProfile).avatar_url : null}
                        name={item.name}
                        id={item.id}
                        size="sm"
                      />
                      <div className="min-w-0 flex-1">
                        <p className="font-medium truncate">{item.name}</p>
                        <p className="text-xs text-muted-foreground truncate">{item.email}</p>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <Badge variant={item.type === 'lead' ? 'secondary' : 'default'} className="text-xs">
                        {item.type}
                      </Badge>
                      {item.intake_status && (
                        <span className="text-xs">
                          {item.intake_status === 'complete' && '✅'}
                          {item.intake_status === 'locked' && '🔒'}
                          {item.intake_status === 'incomplete' && '⏳'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </aside>

        {/* Right Panel - Detail View */}
        <div className={`${mobileView === 'detail' ? 'col-span-12' : 'hidden'} md:col-span-8 lg:col-span-9 md:block bg-white p-6`}>
          {selectedItem ? (
            <>
              {/* Mobile Back Button */}
              <div className="md:hidden mb-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMobileView('list')}
                  className="flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                  Back to List
                </Button>
              </div>

              <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>{selectedItem.name}</span>
                  <div className="flex items-center gap-2">
                    <Badge variant={selectedItem.type === 'lead' ? 'secondary' : 'default'}>
                      {selectedItem.type}
                    </Badge>
                    {selectedItem.intake_status && (
                      <Badge variant="outline">
                        {selectedItem.intake_status}
                      </Badge>
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {selectedItem.type === 'lead' && editingLead && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="lead-name">Contact Name</Label>
                        <Input
                          id="lead-name"
                          value={editingLead.primary_contact_name || ''}
                          onChange={(e) => setEditingLead({...editingLead, primary_contact_name: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="lead-email">Email</Label>
                        <Input
                          id="lead-email"
                          type="email"
                          value={editingLead.primary_contact_email || ''}
                          onChange={(e) => setEditingLead({...editingLead, primary_contact_email: e.target.value})}
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="lead-phone">Phone</Label>
                        <Input
                          id="lead-phone"
                          value={editingLead.primary_contact_phone || ''}
                          onChange={(e) => setEditingLead({...editingLead, primary_contact_phone: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="lead-status">Status</Label>
                        <Select
                          value={editingLead.status}
                          onValueChange={(value) => setEditingLead({...editingLead, status: value as any})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="new">New</SelectItem>
                            <SelectItem value="qualified">Qualified</SelectItem>
                            <SelectItem value="proposal_sent">Proposal Sent</SelectItem>
                            <SelectItem value="waiting">Waiting</SelectItem>
                            <SelectItem value="won">Won</SelectItem>
                            <SelectItem value="lost">Lost</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="lead-notes">Notes</Label>
                      <Textarea
                        id="lead-notes"
                        value={editingLead.notes || ''}
                        onChange={(e) => setEditingLead({...editingLead, notes: e.target.value})}
                        rows={3}
                      />
                    </div>
                    <div className="text-sm text-muted-foreground">
                      <p>Type: {editingLead.lead_type}</p>
                      <p>Source: {editingLead.source}</p>
                      <p>Created: {new Date(editingLead.created_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}

                {selectedItem.type === 'user' && editingUser && (
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="user-name">Full Name</Label>
                        <Input
                          id="user-name"
                          value={editingUser.full_name || ''}
                          onChange={(e) => setEditingUser({...editingUser, full_name: e.target.value})}
                          disabled={editingUser.intake_status === 'locked'}
                        />
                      </div>
                      <div>
                        <Label htmlFor="user-email">Email</Label>
                        <Input
                          id="user-email"
                          type="email"
                          value={editingUser.email || ''}
                          disabled
                          className="bg-gray-50"
                        />
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="user-phone">Phone</Label>
                        <Input
                          id="user-phone"
                          value={editingUser.phone_number || ''}
                          onChange={(e) => setEditingUser({...editingUser, phone_number: e.target.value})}
                          disabled={editingUser.intake_status === 'locked'}
                        />
                      </div>
                      <div>
                        <Label>Roles</Label>
                        <div className="flex gap-1 mt-1">
                          {editingUser.roles.map(role => (
                            <Badge key={role} variant="secondary">{role}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label>Student Status</Label>
                        <p className="text-sm">{editingUser.student_status || 'N/A'}</p>
                      </div>
                      <div>
                        <Label>Tutor Status</Label>
                        <p className="text-sm">{editingUser.tutor_status || 'N/A'}</p>
                      </div>
                      <div>
                        <Label>Admin Status</Label>
                        <p className="text-sm">{editingUser.admin_status || 'N/A'}</p>
                      </div>
                    </div>
                    {editingUser.intake_status && (
                      <div className="border-t pt-4">
                        <h4 className="font-medium mb-2">Intake Information</h4>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <Label>Status</Label>
                            <p className="capitalize">{editingUser.intake_status}</p>
                          </div>
                          <div>
                            <Label>Completed At</Label>
                            <p>{editingUser.intake_completed_at ? new Date(editingUser.intake_completed_at).toLocaleDateString() : 'N/A'}</p>
                          </div>
                        </div>
                        {editingUser.intake_notes && (
                          <div className="mt-2">
                            <Label>Notes</Label>
                            <p className="text-sm text-muted-foreground">{editingUser.intake_notes}</p>
                          </div>
                        )}
                      </div>
                    )}
                    <div className="text-sm text-muted-foreground">
                      <p>Created: {new Date(editingUser.created_at).toLocaleDateString()}</p>
                      <p>Updated: {new Date(editingUser.updated_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
            </>
          ) : (
            <div className="flex items-center justify-center h-64">
              <p className="text-muted-foreground">Select an item to view details</p>
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
