'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'

import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import AppHeader from '@/components/app-header'
import Footer from '@/components/footer'

// Custom hook for scroll animations
function useScrollAnimation() {
  const [isVisible, setIsVisible] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      }
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => {
      if (ref.current) {
        observer.unobserve(ref.current)
      }
    }
  }, [])

  return [ref, isVisible] as const
}

// Animated section component
function AnimatedSection({ children, className = '', delay = 0 }: { 
  children: React.ReactNode, 
  className?: string,
  delay?: number 
}) {
  const [ref, isVisible] = useScrollAnimation()

  return (
    <div
      ref={ref}
      className={`transition-all duration-700 ease-out ${
        isVisible 
          ? 'opacity-100 translate-y-0' 
          : 'opacity-0 translate-y-8'
      } ${className}`}
      style={{ 
        transitionDelay: isVisible ? `${delay}ms` : '0ms' 
      }}
    >
      {children}
    </div>
  )
}

export default function TutoringPage() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }
    
    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  return (
    <div className="page-container">
      <AppHeader />

      {/* Hero Section - Tutor Focused - Takes remaining viewport height */}
      <div className="h-screen flex flex-col overflow-hidden relative">
        <section className="bg-black text-white flex-1 flex items-center overflow-hidden relative">
          <div className="w-full px-4 md:px-8 lg:px-32">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              {/* Part 1: Tutor-Focused Hero Copy */}
              <div className="lg:col-span-1 space-y-6 md:space-y-10 text-center lg:text-left">
                <div className="space-y-4 md:space-y-6">
                  <p className="text-gray-300 text-lg md:text-xl lg:text-2xl font-medium">Tutor on a trusted platform.</p>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight tracking-tight">
                    Powerful tutoring and classes.
                  </h1>
                </div>

                <p className="text-base md:text-lg lg:text-xl xl:text-2xl text-white leading-relaxed max-w-lg mx-auto lg:mx-0">
                  Set your own rate, we'll connect you with students, you earn money on your schedule. Get started today.
                </p>

                <div className="pt-4 md:pt-6 space-y-4">
                  <div className="flex justify-center lg:justify-start">
                    <Link href="/auth">
                      <button className="bg-white text-black hover:bg-gray-100 text-lg px-8 py-4 rounded-full font-medium transition-colors duration-200 flex items-center space-x-2">
                        <span>Become an online tutor</span>
                      </button>
                    </Link>
                  </div>
                </div>
              </div>

              {/* Large Tutor Profile Image - Same as homepage */}
              <div className="hidden lg:block lg:col-span-1 relative overflow-hidden z-10">
                <img
                  src="https://framerusercontent.com/images/skP11X6IqJufQvxfS58erYQMbns.png?scale-down-to=2048"
                  alt="Tutor profile interface"
                  className="w-full h-full object-cover object-left"
                  style={{width: '120%', minHeight: '100%', maxHeight: '100%'}}
                />
              </div>
            </div>
          </div>
        </section>
      </div>

      <main>
        {/* Why Online Tutoring Section */}
      <section className="bg-white py-20 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Why online tutoring.</h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              Online tutoring is the perfect job for students, recent graduates, and teachers. You'll make money, gain CV-boosting skills, and help others.
            </p>
            <div className="mt-8">
              <Link href="/auth">
                <button className="bg-black text-white hover:bg-gray-800 text-lg px-8 py-4 rounded-full font-medium transition-colors duration-200">
                  Get started today
                </button>
              </Link>
            </div>
          </div>

          <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
            {/* Set your own rate */}
            <div className="text-center space-y-6">
              <div className="bg-gray-50 rounded-2xl p-8 h-64 flex flex-col justify-center">
                <div className="text-6xl mb-4">📅</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Set your own rate</h3>
                <p className="text-gray-600 text-sm">Choose your rate and change it anytime. Offer discounts to students you regularly see.</p>
              </div>
            </div>

            {/* We'll connect you with students */}
            <div className="text-center space-y-6">
              <div className="bg-gray-50 rounded-2xl p-8 h-64 flex flex-col justify-center">
                <div className="text-6xl mb-4">👥</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">We'll connect you with students</h3>
                <p className="text-gray-600 text-sm">Let us do the work of connecting you with students while you focus on teaching.</p>
              </div>
            </div>

            {/* Grow and be fulfilled */}
            <div className="text-center space-y-6">
              <div className="bg-gray-50 rounded-2xl p-8 h-64 flex flex-col justify-center">
                <div className="text-6xl mb-4">🌱</div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Grow and be fulfilled</h3>
                <p className="text-gray-600 text-sm">Develop skills for life and help shape the education of students who need it most.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Be discovered in the Popless marketplace Section */}
      <section className="bg-gray-50 py-20 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h2 className="text-4xl md:text-5xl font-bold text-gray-900">
                Be discovered in the ProTutor marketplace.
              </h2>
              <p className="text-lg text-gray-600">
                Opt-in to be found on the ProTutor marketplace by 1000's of students looking for help.
              </p>
              <div>
                <button className="bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 text-lg px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                  Learn more
                </button>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="bg-green-100 rounded-xl p-4 h-32 flex items-center justify-center">
                  <span className="text-sm font-medium text-green-800">Write and publish your first story</span>
                </div>
                <div className="bg-blue-100 rounded-xl p-4 h-40 flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-800">Stanford University | Intro to robotics</span>
                </div>
                <div className="bg-purple-100 rounded-xl p-4 h-32 flex items-center justify-center">
                  <span className="text-sm font-medium text-purple-800">Learn drawing with an artist</span>
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="bg-gray-100 rounded-xl p-4 h-32 flex items-center justify-center">
                  <span className="text-sm font-medium text-gray-800">Make progress in wellness</span>
                </div>
                <div className="bg-orange-100 rounded-xl p-4 h-40 flex items-center justify-center">
                  <span className="text-sm font-medium text-orange-800">Musician | Learn guitar</span>
                </div>
                <div className="bg-yellow-100 rounded-xl p-4 h-32 flex items-center justify-center">
                  <span className="text-sm font-medium text-yellow-800">Study for SAT and ACT</span>
                </div>
                <div className="bg-green-100 rounded-xl p-4 h-32 flex items-center justify-center">
                  <span className="text-sm font-medium text-green-800">Horticulturalist | Intro to horticulture</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

        {/* Section 1 - Create Your Profile */}
        <AnimatedSection>
          <div className="bg-gray-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">1</div>
                      <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">Step One</span>
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900">
                      Create Your Professional Profile
                    </h2>
                    <p className="text-lg text-gray-600">
                      Showcase your expertise, credentials, and teaching experience. Build a compelling profile
                      that attracts students and demonstrates your qualifications in IB & AP subjects.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">Upload credentials and certifications</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">Set your hourly rates and availability</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">✓</span>
                        </div>
                        <span className="text-gray-700">Highlight your teaching specialties</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="mt-10 lg:mt-0">
                    <div className="transform -rotate-1 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">📝</div>
                          <p className="text-gray-500 font-medium">poplanding2.png</p>
                          <p className="text-gray-400 text-sm">Profile creation interface</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Section 2 - Manage Students */}
        <AnimatedSection>
          <div className="bg-white py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="order-2 lg:order-1">
                    <div className="transform rotate-2 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">📊</div>
                          <p className="text-gray-500 font-medium">poplanding3.png</p>
                          <p className="text-gray-400 text-sm">Student management dashboard</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="space-y-6 order-1 lg:order-2">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">2</div>
                      <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">Step Two</span>
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900">
                      Manage Your Students
                    </h2>
                    <p className="text-lg text-gray-600">
                      Keep track of all your students, their progress, and upcoming sessions in one organized dashboard.
                      Monitor performance and provide personalized feedback.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📊</span>
                        </div>
                        <span className="text-gray-700">Track student progress and performance</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📝</span>
                        </div>
                        <span className="text-gray-700">Manage assignments and homework</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">💬</span>
                        </div>
                        <span className="text-gray-700">Communicate directly with students</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Section 3 - Earn & Schedule */}
        <AnimatedSection>
          <div className="bg-gray-50 py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">3</div>
                      <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">Step Three</span>
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900">
                      Earn & Grow Your Business
                    </h2>
                    <p className="text-lg text-gray-600">
                      Set your own rates, manage your schedule, and grow your tutoring business.
                      Track earnings, receive payments securely, and build your reputation.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">💰</span>
                        </div>
                        <span className="text-gray-700">Set your own hourly rates</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📅</span>
                        </div>
                        <span className="text-gray-700">Flexible scheduling and availability</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">⭐</span>
                        </div>
                        <span className="text-gray-700">Build reputation through reviews</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="mt-10 lg:mt-0">
                    <div className="transform -rotate-2 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">💰</div>
                          <p className="text-gray-500 font-medium">poplanding4.png</p>
                          <p className="text-gray-400 text-sm">Earnings and scheduling interface</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Section 4 - Additional Features */}
        <AnimatedSection>
          <div className="bg-white py-20">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
                <AnimatedSection delay={200}>
                  <div className="order-2 lg:order-1">
                    <div className="transform rotate-1 shadow-xl">
                      <div className="bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg h-80 flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-gray-400 text-4xl mb-3">🎯</div>
                          <p className="text-gray-500 font-medium">poplanding5.png</p>
                          <p className="text-gray-400 text-sm">Advanced platform features</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <div className="space-y-6 order-1 lg:order-2">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="w-8 h-8 bg-gray-800 rounded-full flex items-center justify-center text-white font-bold">4</div>
                      <span className="text-sm font-medium text-gray-500 uppercase tracking-wide">Advanced Features</span>
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900">
                      Everything You Need to Succeed
                    </h2>
                    <p className="text-lg text-gray-600">
                      Our platform provides all the tools and resources you need for tutoring success.
                      From analytics to communication tools, we&apos;ve got you covered.
                    </p>
                    <ul className="space-y-3">
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">📈</span>
                        </div>
                        <span className="text-gray-700">Detailed analytics and insights</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">🔧</span>
                        </div>
                        <span className="text-gray-700">Professional tutoring tools</span>
                      </li>
                      <li className="flex items-center space-x-3">
                        <div className="w-5 h-5 bg-gray-800 rounded-full flex items-center justify-center">
                          <span className="text-white text-xs">🏆</span>
                        </div>
                        <span className="text-gray-700">Achievement tracking and rewards</span>
                      </li>
                    </ul>
                  </div>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* Final CTA Section */}
        <AnimatedSection>
          <div className="bg-gray-900 py-20">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
              <h2 className="text-4xl font-bold text-white mb-6">
                Ready to Start Teaching?
              </h2>
              <p className="text-xl text-gray-300 mb-8">
                Join thousands of tutors who are already earning money sharing their expertise on our platform.
              </p>
              <div className="space-y-4">
                <AnimatedSection delay={200}>
                  <Link href="/auth">
                    <button className="bg-white text-gray-900 px-8 py-4 rounded-md text-lg font-medium hover:bg-gray-100 transition-colors duration-200">
                      Get started today
                    </button>
                  </Link>
                </AnimatedSection>
                <AnimatedSection delay={400}>
                  <p className="text-sm text-gray-400">
                    No setup fees • Keep 85% of your earnings • Cancel anytime
                  </p>
                </AnimatedSection>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* We support you every step of the way Section */}
        <AnimatedSection>
          <div className="bg-white py-20">
            <div className="max-w-7xl mx-auto px-4">
              <div className="text-center mb-16">
                <p className="text-sm font-medium text-gray-500 uppercase tracking-wide mb-4">All-in-one tutoring technology.</p>
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  We support you every step of the way.
                </h2>
              </div>

              <div className="grid md:grid-cols-3 gap-8 lg:gap-12">
                {/* Row 1 */}
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">💰</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Set your price</h3>
                  <p className="text-gray-600 text-sm">Set how much you want to charge and offer discounts.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">📅</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Flexible scheduling</h3>
                  <p className="text-gray-600 text-sm">Advanced scheduling tools to help you manage your time.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">🌍</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Global payments</h3>
                  <p className="text-gray-600 text-sm">Accept payments in 45 currencies from 140 countries.</p>
                </div>

                {/* Row 2 */}
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">📊</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">All-in-one dashboard</h3>
                  <p className="text-gray-600 text-sm">The only platform designed for tutoring professionals.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">📦</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Meeting packages</h3>
                  <p className="text-gray-600 text-sm">Offer discounts to people who pre-pay for multiple meetings.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">💬</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Messaging</h3>
                  <p className="text-gray-600 text-sm">Securely send messages, photos, videos, and documents.</p>
                </div>

                {/* Row 3 */}
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">📹</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Video calls</h3>
                  <p className="text-gray-600 text-sm">Support for up to 200 people with breakout rooms and whiteboards.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">🎟️</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Coupon discounts</h3>
                  <p className="text-gray-600 text-sm">Create personalized coupons that can be applied at checkout.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">📅</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Calendar integration</h3>
                  <p className="text-gray-600 text-sm">Connect Google Calendar to easily managing your availability.</p>
                </div>

                {/* Row 4 */}
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">⚡</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Instant booking</h3>
                  <p className="text-gray-600 text-sm">Accept bookings from students automatically or one by one.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">📈</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Analytics</h3>
                  <p className="text-gray-600 text-sm">Track your earnings, bookings, minutes worked, and more.</p>
                </div>

                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-gray-100 rounded-xl flex items-center justify-center mx-auto">
                    <span className="text-2xl">❌</span>
                  </div>
                  <h3 className="text-lg font-bold text-gray-900">Cancellation policies</h3>
                  <p className="text-gray-600 text-sm">Choose a cancellation policy that works for you.</p>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>

        {/* In case you missed anything Section */}
        <AnimatedSection>
          <div className="bg-gray-50 py-20">
            <div className="max-w-4xl mx-auto px-4">
              <div className="text-center mb-16">
                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  In case you missed anything.
                </h2>
              </div>

              <div className="space-y-6">
                <div className="bg-white rounded-lg border border-gray-200">
                  <button className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span className="text-lg font-medium text-gray-900">What is ProTutor?</span>
                    <span className="text-gray-400">▼</span>
                  </button>
                </div>

                <div className="bg-white rounded-lg border border-gray-200">
                  <button className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span className="text-lg font-medium text-gray-900">How do payments work?</span>
                    <span className="text-gray-400">▼</span>
                  </button>
                </div>

                <div className="bg-white rounded-lg border border-gray-200">
                  <button className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span className="text-lg font-medium text-gray-900">How long does it take to get set up?</span>
                    <span className="text-gray-400">▼</span>
                  </button>
                </div>

                <div className="bg-white rounded-lg border border-gray-200">
                  <button className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span className="text-lg font-medium text-gray-900">Can I tutor my existing students on ProTutor?</span>
                    <span className="text-gray-400">▼</span>
                  </button>
                </div>

                <div className="bg-white rounded-lg border border-gray-200">
                  <button className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span className="text-lg font-medium text-gray-900">How do I set when I'm available for tutoring?</span>
                    <span className="text-gray-400">▼</span>
                  </button>
                </div>

                <div className="bg-white rounded-lg border border-gray-200">
                  <button className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors">
                    <span className="text-lg font-medium text-gray-900">How does ProTutor make money?</span>
                    <span className="text-gray-400">▼</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </AnimatedSection>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  )
}
