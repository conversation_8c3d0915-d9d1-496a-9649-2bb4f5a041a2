@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
}

/* Consistent design system */
.page-container {
  @apply min-h-screen bg-white;
}

.header-standard {
  @apply bg-white w-full h-16 flex-shrink-0 border-b border-gray-100;
}

.nav-right {
  @apply flex items-center space-x-6 pr-4;
}

.nav-links {
  @apply hidden md:flex items-center space-x-6;
}

.nav-link {
  @apply text-gray-600 hover:text-gray-900 font-medium transition-colors;
}

.nav-link-active {
  @apply text-gray-900 font-medium border-b-2 border-gray-900;
}

.auth-ui {
  @apply flex items-center space-x-3;
}

.btn-primary {
  @apply bg-black text-white hover:bg-gray-800 transition-colors rounded-full px-4 py-2 font-medium;
}

.btn-secondary {
  @apply bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 transition-colors rounded-md px-4 py-2 font-medium;
}

.text-body {
  @apply text-gray-700 leading-relaxed;
}

.text-muted {
  @apply text-gray-500;
}

.hero-title {
  @apply text-4xl md:text-6xl font-bold tracking-tight text-gray-900;
}

.hero-subtitle {
  @apply text-xl md:text-2xl text-gray-600 leading-relaxed;
}


