import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
// Filen API helper functions (using REST API instead of SDK)
// TODO: Implement when upgrading from base64 to Filen storage
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const filenLogin = async () => {
  const email = process.env.NEXT_PUBLIC_FILEN_EMAIL
  const password = process.env.NEXT_PUBLIC_FILEN_PASSWORD

  console.log('🔧 Filen credentials check:', {
    email: email ? 'SET' : 'MISSING',
    password: password ? 'SET' : 'MISSING'
  })

  if (!email || !password) {
    throw new Error('Filen credentials not found in environment variables')
  }

  console.log('🔑 Attempting Filen API login...')

  const response = await fetch('https://gateway.filen.io/v3/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      email,
      password,
      twoFactorCode: process.env.NEXT_PUBLIC_FILEN_2FA_CODE || '000000',
    }),
  })

  if (!response.ok) {
    const errorText = await response.text()
    throw new Error(`Filen login failed: ${response.status} ${response.statusText} - ${errorText}`)
  }

  const data = await response.json()
  console.log('✅ Filen login successful!')

  return {
    apiKey: data.apiKey,
    masterKeys: data.masterKeys,
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get the uploaded file from form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const userId = formData.get('userId') as string

    if (!file || !userId) {
      return NextResponse.json(
        { error: 'File and userId are required' },
        { status: 400 }
      )
    }

    // Validate file type and size
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Only JPEG, PNG, and WebP images are allowed' },
        { status: 400 }
      )
    }

    const maxSize = 5 * 1024 * 1024 // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size must be less than 5MB' },
        { status: 400 }
      )
    }

    // Verify user authentication
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user || user.id !== userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get current profile picture URL to delete old file later (unused for now)
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { data: profile } = await supabase
      .from('profiles')
      .select('profile_picture_url')
      .eq('id', userId)
      .single()

    // For now, let's use a simple base64 approach until Filen API is working
    // TODO: Replace with actual Filen upload later
    console.log('📤 Converting image to base64 for temporary storage...')

    // Convert file to base64 data URL for temporary storage
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)
    const base64 = buffer.toString('base64')
    const dataUrl = `data:${file.type};base64,${base64}`

    console.log('✅ Image converted to base64 successfully')

    // Update user profile with new profile picture URL (base64 data URL)
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ profile_picture_url: dataUrl })
      .eq('id', userId)

    if (updateError) {
      console.error('Database update error:', updateError)
      return NextResponse.json(
        { error: 'Failed to update profile' },
        { status: 500 }
      )
    }

    // Note: Old profile pictures are automatically replaced in database
    // TODO: Implement proper file cleanup when we switch to Filen storage

    console.log('✅ Profile picture updated successfully!')

    return NextResponse.json({
      success: true,
      profilePictureUrl: dataUrl,
      message: 'Profile picture uploaded successfully'
    })

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload profile picture' },
      { status: 500 }
    )
  }
}
