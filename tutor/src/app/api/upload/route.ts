import { NextRequest, NextResponse } from 'next/server'
import { v2 as cloudinary } from 'cloudinary'
import { createClient } from '@/lib/supabase/server'

export const runtime = 'nodejs'

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
})

export async function POST(request: NextRequest) {
  try {
    // Verify user authentication
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const uploadType = formData.get('type') as string || 'profile'
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      return NextResponse.json({ error: 'File must be an image' }, { status: 400 })
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size must be less than 5MB' }, { status: 400 })
    }

    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    const hasCloudinary = !!(process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME && process.env.CLOUDINARY_API_KEY && process.env.CLOUDINARY_API_SECRET)

    // Determine folder and transformation based on upload type
    let folder = 'tutor-platform/users/profile-pictures'
    let transformation: any = {
      width: 400,
      height: 400,
      crop: 'fill',
      gravity: 'face',
      quality: 'auto',
      format: 'webp'
    }

    if (uploadType === 'certificate') {
      folder = 'tutor-platform/tutors/certificates'
      transformation = {
        width: 1200,
        height: 900,
        crop: 'fit',
        quality: 'auto',
        format: 'webp'
      }
    }

    // If Cloudinary is not configured, fallback to base64 data URL stored in profiles
    if (!hasCloudinary) {
      const base64 = buffer.toString('base64')
      const dataUrl = `data:${file.type};base64,${base64}`
      const supabaseUpdate = await supabase
        .from('profiles')
        .update({ profile_picture_url: dataUrl })
        .eq('id', user.id)
      if (supabaseUpdate.error) {
        console.error('DB update error (fallback):', supabaseUpdate.error)
        return NextResponse.json({ error: 'Failed to update profile image' }, { status: 500 })
      }
      return NextResponse.json({ url: dataUrl })
    }

    try {
      // Upload to Cloudinary
      const result = await new Promise((resolve, reject) => {
        cloudinary.uploader.upload_stream(
          {
            folder: folder,
            public_id: `${user.id}_${Date.now()}`,
            transformation: transformation,
            overwrite: true,
            resource_type: 'image'
          },
          (error, result) => {
            if (error) reject(error)
            else resolve(result)
          }
        ).end(buffer)
      })

      const uploadResult = result as any

      return NextResponse.json({
        url: uploadResult.secure_url,
        publicId: uploadResult.public_id,
        width: uploadResult.width,
        height: uploadResult.height
      })
    } catch (e: any) {
      console.error('Cloudinary upload error:', e)
      // Fallback to base64 data URL if Cloudinary upload fails (dev-friendly)
      try {
        const base64 = buffer.toString('base64')
        const dataUrl = `data:${file.type};base64,${base64}`
        const supabaseUpdate = await supabase
          .from('profiles')
          .update({ profile_picture_url: dataUrl })
          .eq('id', user.id)
        if (supabaseUpdate.error) {
          console.error('DB update error (fallback):', supabaseUpdate.error)
          return NextResponse.json({ error: 'Upload failed (Cloudinary + fallback)' }, { status: 500 })
        }
        return NextResponse.json({ url: dataUrl, fallback: true })
      } catch (fbErr) {
        console.error('Fallback error:', fbErr)
        return NextResponse.json({ error: e?.message || 'Cloudinary upload failed' }, { status: 500 })
      }
    }

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { error: 'Failed to upload image' },
      { status: 500 }
    )
  }
}
