'use client'

import { useEffect, useState, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import Logo from '@/components/logo'
import ProfileDropdown from '@/components/profile-dropdown'
import Footer from '@/components/footer'
import EnhancedImageUpload from '@/components/enhanced-image-upload'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Settings, User as UserIcon, GraduationCap, Shield } from 'lucide-react'

interface Profile {
  id: string
  full_name: string
  email: string
  bio: string | null
  subjects: string[] | null
  education: string | null
  experience_years: number | null
  certifications: string[] | null
  languages: string[] | null
  hourly_rate: number | null
  roles: string[] | null
  profile_picture_url: string | null
}

function AccountPageContent() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeSection, setActiveSection] = useState('personal')


  // Form states
  const [fullName, setFullName] = useState('')
  const [bio, setBio] = useState('')
  const [subjects, setSubjects] = useState<string[]>([])
  const [education, setEducation] = useState('')
  const [experienceYears, setExperienceYears] = useState(0)
  const [certifications, setCertifications] = useState<string[]>([''])
  const [languages, setLanguages] = useState<string[]>([''])
  const [subjectRates, setSubjectRates] = useState<{[key: string]: number}>({})
  const [subjectExperience, setSubjectExperience] = useState<{[key: string]: number}>({})
  const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(null)

  const router = useRouter()
  const searchParams = useSearchParams()
  const supabase = createClient()

  const predefinedSubjects = [
    'Mathematics',
    'Sciences',
    'Literature',
    'Social Studies',
    'Languages'
  ]

  const commonLanguages = [
    'English', 'Spanish', 'French', 'German', 'Italian', 'Portuguese', 'Turkish'
  ]

  const otherLanguages = [
    'Afrikaans', 'Albanian', 'Amharic', 'Arabic', 'Armenian', 'Azerbaijani',
    'Bengali', 'Bosnian', 'Bulgarian', 'Burmese', 'Catalan', 'Chinese (Mandarin)',
    'Chinese (Cantonese)', 'Croatian', 'Czech', 'Danish', 'Dutch', 'Estonian',
    'Filipino', 'Finnish', 'Georgian', 'Greek', 'Gujarati', 'Hebrew', 'Hindi',
    'Hungarian', 'Icelandic', 'Indonesian', 'Irish', 'Japanese', 'Javanese',
    'Kannada', 'Kazakh', 'Khmer', 'Korean', 'Kurdish', 'Kyrgyz', 'Lao', 'Latvian',
    'Lithuanian', 'Macedonian', 'Malay', 'Malayalam', 'Maltese', 'Marathi',
    'Mongolian', 'Nepali', 'Norwegian', 'Pashto', 'Persian', 'Polish', 'Punjabi',
    'Romanian', 'Russian', 'Serbian', 'Sinhala', 'Slovak', 'Slovenian', 'Somali',
    'Swahili', 'Swedish', 'Tajik', 'Tamil', 'Telugu', 'Thai', 'Tibetan', 'Turkish',
    'Turkmen', 'Ukrainian', 'Urdu', 'Uzbek', 'Vietnamese', 'Welsh', 'Xhosa', 'Yoruba', 'Zulu'
  ]

  const bioPlaceholder = `Write a compelling bio that showcases your expertise and teaching style. Here are some examples:

📚 Mathematics Tutor Example:
"Hi! I'm Sarah, a passionate mathematics tutor with 5 years of experience helping students excel in algebra, calculus, and statistics. I hold a Master's degree in Mathematics from UCLA and believe that every student can master math with the right approach. My teaching style focuses on breaking down complex concepts into simple, digestible steps while building confidence through practice and encouragement."

🔬 Science Tutor Example:
"Hello! I'm Dr. Ahmed, a chemistry and biology tutor with a PhD in Biochemistry. I've helped over 200 students improve their grades and develop a genuine love for science. I use real-world examples and hands-on explanations to make complex scientific concepts accessible and engaging. Whether you're struggling with organic chemistry or preparing for medical school, I'm here to guide you every step of the way."

📖 Language Tutor Example:
"¡Hola! I'm Maria, a native Spanish speaker and certified language instructor with 8 years of teaching experience. I specialize in conversational Spanish, grammar, and cultural immersion. My interactive teaching method combines traditional learning with modern techniques like role-playing and multimedia resources. I've helped students of all levels achieve fluency and confidence in Spanish."

Your bio should be at least 50 characters and highlight your qualifications, teaching experience, and what makes you unique as a tutor.`

  useEffect(() => {
    loadUserAndProfile()

    // Check for tab parameter to auto-open tutor application
    const tab = searchParams.get('tab')
    if (tab === 'tutor-apply') {
      setActiveSection('tutor-apply')
    }
  }, [searchParams])

  const loadUserAndProfile = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth')
        return
      }

      setUser(user)

      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profile) {
        setProfile(profile)
        setFullName(profile.full_name || '')
        setBio(profile.bio || '')
        setSubjects(profile.subjects || [])
        setEducation(profile.education || '')
        setExperienceYears(profile.experience_years || 0)
        setCertifications(profile.certifications || [''])
        setLanguages(profile.languages || [''])
        // Initialize subject rates - use general rate for all subjects if available
        const generalRate = profile.hourly_rate ? profile.hourly_rate / 100 : 25
        const generalExperience = profile.experience_years || 0
        const initialRates: {[key: string]: number} = {}
        const initialExperience: {[key: string]: number} = {}
        predefinedSubjects.forEach(subject => {
          initialRates[subject] = generalRate
          initialExperience[subject] = generalExperience
        })
        setSubjectRates(initialRates)
        setSubjectExperience(initialExperience)
        setProfilePictureUrl(profile.profile_picture_url || null)
      }
    } catch (error) {
      console.error('Error loading profile:', error)
    } finally {
      setLoading(false)
    }
  }



  const handleSave = async (section: string) => {
    if (!profile) return

    setSaving(true)
    try {
      if (section === 'personal') {
        const { error } = await supabase
          .from('profiles')
          .update({
            full_name: fullName.trim(),
            profile_picture_url: profilePictureUrl
          })
          .eq('id', profile.id)

        if (error) throw error
        alert('Personal information updated successfully!')
      }

      if (section === 'tutor') {
        // Validation for tutor application
        if (!bio.trim() || bio.trim().length < 50) {
          throw new Error('Bio must be at least 50 characters long')
        }
        if (subjects.length === 0) {
          throw new Error('Please select at least one subject')
        }
        if (!education.trim()) {
          throw new Error('Education background is required')
        }
        if (!certifications.some(cert => cert.trim())) {
          throw new Error('At least one qualification or certification is required')
        }

        // Validate that selected subjects have rates set
        const selectedSubjects = subjects.filter(s => s)
        const invalidRates = selectedSubjects.filter(subject => !subjectRates[subject] || subjectRates[subject] < 15)
        if (invalidRates.length > 0) {
          throw new Error(`Please set hourly rates (minimum $15) for: ${invalidRates.join(', ')}`)
        }

        // Validate that experience is set for selected subjects (can be 0)
        const invalidExperience = selectedSubjects.filter(subject => subjectExperience[subject] === undefined || subjectExperience[subject] === null)
        if (invalidExperience.length > 0) {
          throw new Error(`Please set experience years (can be 0) for: ${invalidExperience.join(', ')}`)
        }

        // Calculate max experience (most relevant for tutors)
        const maxExperience = Object.values(subjectExperience).length > 0
          ? Math.max(...Object.values(subjectExperience))
          : 0

        const { error: profileError } = await supabase
          .from('profiles')
          .update({
            bio: bio.trim(),
            subjects: subjects.filter(s => s),
            education: education.trim(),
            experience_years: maxExperience,
            certifications: certifications.filter(c => c.trim()),
            languages: languages.filter(l => l.trim()),
            hourly_rate: Math.round(Object.values(subjectRates).reduce((sum, rate) => sum + rate, 0) / Object.values(subjectRates).length * 100)
          })
          .eq('id', profile.id)

        if (profileError) throw profileError

        // If not already a tutor, request tutor role using RPC function
        if (!profile.roles?.includes('tutor')) {
          const { error: roleError } = await supabase
            .rpc('add_user_role', {
              user_id: profile.id,
              new_role: 'tutor'
            })

          if (roleError) throw roleError
          alert('Tutor application submitted successfully! Pending admin approval.')
        } else {
          alert('Tutor profile updated successfully!')
        }
      }

      await loadUserAndProfile()
    } catch (error: any) {
      alert(error.message || 'Error saving changes')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user || !profile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Please sign in</h1>
          <Button onClick={() => router.push('/auth')}>
            Sign In
          </Button>
        </div>
      </div>
    )
  }

  const isAdmin = profile.roles?.includes('admin')
  const isTutor = profile.roles?.includes('tutor')

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Logo />
            <ProfileDropdown user={user} />
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          {/* Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Account Settings</h1>
            <p className="text-gray-600">Manage your personal information and preferences</p>
          </div>

          {/* Navigation Tabs */}
          <div className="border-b border-gray-200">
            <nav className="flex space-x-2 px-6 py-2">
              <Button
                onClick={() => setActiveSection('personal')}
                variant={activeSection === 'personal' ? 'default' : 'ghost'}
                size="sm"
                className="h-auto py-3 px-4"
              >
                <UserIcon className="w-4 h-4 mr-2" />
                Personal Information
              </Button>

              {isTutor && (
                <Button
                  onClick={() => setActiveSection('tutor')}
                  variant={activeSection === 'tutor' ? 'default' : 'ghost'}
                  size="sm"
                  className="h-auto py-3 px-4"
                >
                  <GraduationCap className="w-4 h-4 mr-2" />
                  Tutor Profile
                </Button>
              )}

              <Button
                onClick={() => setActiveSection('settings')}
                variant={activeSection === 'settings' ? 'default' : 'ghost'}
                size="sm"
                className="h-auto py-3 px-4"
              >
                <Settings className="w-4 h-4 mr-2" />
                Preferences
              </Button>

              {isAdmin && (
                <Button
                  onClick={() => router.push('/admin')}
                  variant="ghost"
                  size="sm"
                  className="h-auto py-3 px-4"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Admin Dashboard
                </Button>
              )}
            </nav>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeSection === 'personal' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">Personal Information</h2>
                
                <EnhancedImageUpload
                  currentImageUrl={profilePictureUrl}
                  onImageChange={setProfilePictureUrl}
                  userId={profile.id}
                  disabled={saving}
                  size="medium"
                  aspectRatio={1}
                  cropShape="round"
                />

                <div>
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    disabled={saving}
                  />
                </div>

                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    value={user.email || ''}
                    disabled
                    className="bg-gray-50"
                  />
                  <p className="text-sm text-gray-500 mt-1">Email cannot be changed here</p>
                </div>

                <Button 
                  onClick={() => handleSave('personal')}
                  disabled={saving}
                  className="bg-gray-900 hover:bg-gray-800"
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </div>
            )}

            {(activeSection === 'tutor' && isTutor) || activeSection === 'tutor-apply' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">
                  {isTutor ? 'Edit Tutor Profile' : 'Apply to Become a Tutor'}
                </h2>

                {!isTutor && (
                  <div className="bg-blue-50 p-4 rounded-lg mb-6">
                    <h3 className="font-semibold text-blue-800 mb-2">Requirements for Approval:</h3>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Complete bio (minimum 50 characters) - showcase your expertise</li>
                      <li>• At least one subject specialization</li>
                      <li>• Educational background (any level welcome)</li>
                      <li>• Qualifications or certifications (formal or informal)</li>
                      <li>• Hourly rates for each subject (minimum $15/hour)</li>
                    </ul>
                    <p className="text-sm text-blue-600 mt-2 font-medium">We welcome tutors of all backgrounds and experience levels!</p>
                  </div>
                )}

                <div>
                  <Label htmlFor="bio">Bio *</Label>
                  <Textarea
                    id="bio"
                    value={bio}
                    onChange={(e) => setBio(e.target.value)}
                    placeholder={bioPlaceholder}
                    rows={8}
                    disabled={saving}
                    className="text-sm"
                  />
                  <p className="text-sm text-gray-500 mt-1">{bio.length}/50 characters minimum</p>
                </div>

                <div>
                  <Label>Subjects, Experience & Rates *</Label>
                  <p className="text-sm text-gray-500 mb-3">Select subjects and set your experience years and hourly rate for each</p>
                  <div className="space-y-3">
                    {predefinedSubjects.map((subject) => (
                      <div key={subject} className="border rounded-lg hover:bg-gray-50">
                        <label className="flex items-center space-x-3 p-4">
                          <input
                            type="checkbox"
                            checked={subjects.includes(subject)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSubjects([...subjects, subject])
                                // Set defaults if not already set
                                if (!subjectRates[subject]) {
                                  setSubjectRates(prev => ({
                                    ...prev,
                                    [subject]: 25
                                  }))
                                }
                                if (!subjectExperience[subject]) {
                                  setSubjectExperience(prev => ({
                                    ...prev,
                                    [subject]: 0
                                  }))
                                }
                              } else {
                                setSubjects(subjects.filter(s => s !== subject))
                                // Remove data when unchecked
                                const newRates = { ...subjectRates }
                                const newExperience = { ...subjectExperience }
                                delete newRates[subject]
                                delete newExperience[subject]
                                setSubjectRates(newRates)
                                setSubjectExperience(newExperience)
                              }
                            }}
                            disabled={saving}
                            className="w-4 h-4"
                          />
                          <span className="font-medium text-gray-900 flex-1">{subject}</span>
                        </label>

                        {subjects.includes(subject) && (
                          <div className="px-4 pb-4 grid grid-cols-2 gap-4">
                            {/* Experience */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Years of Experience
                              </label>
                              <div className="flex items-center space-x-2">
                                <Input
                                  type="number"
                                  min="0"
                                  max="50"
                                  value={subjectExperience[subject] || ''}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setSubjectExperience(prev => ({
                                      ...prev,
                                      [subject]: value === '' ? 0 : parseInt(value) || 0
                                    }))
                                  }}
                                  onBlur={(e) => {
                                    const value = parseInt(e.target.value) || 0
                                    setSubjectExperience(prev => ({
                                      ...prev,
                                      [subject]: Math.max(0, value)
                                    }))
                                  }}
                                  placeholder="0"
                                  disabled={saving}
                                  className="w-20 text-center"
                                />
                                <span className="text-sm text-gray-500">years</span>
                              </div>
                            </div>

                            {/* Hourly Rate */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Hourly Rate
                              </label>
                              <div className="flex items-center space-x-2">
                                <span className="text-sm text-gray-500">$</span>
                                <Input
                                  type="number"
                                  min="15"
                                  max="200"
                                  value={subjectRates[subject] || ''}
                                  onChange={(e) => {
                                    const value = e.target.value
                                    setSubjectRates(prev => ({
                                      ...prev,
                                      [subject]: value === '' ? 15 : parseFloat(value) || 15
                                    }))
                                  }}
                                  onBlur={(e) => {
                                    const value = parseFloat(e.target.value) || 15
                                    setSubjectRates(prev => ({
                                      ...prev,
                                      [subject]: Math.max(15, value)
                                    }))
                                  }}
                                  placeholder="25"
                                  disabled={saving}
                                  className="w-20 text-center"
                                />
                                <span className="text-sm text-gray-500">/hr</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  {subjects.length === 0 && (
                    <p className="text-sm text-gray-500 mt-2 italic">Please select at least one subject</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="education">Education Background *</Label>
                  <Input
                    id="education"
                    value={education}
                    onChange={(e) => setEducation(e.target.value)}
                    placeholder="e.g., Bachelor's in Mathematics, High School Diploma, Teaching Certificate, Self-taught expert"
                    disabled={saving}
                  />
                  <p className="text-sm text-gray-500 mt-1">Any educational background is welcome - formal degree, certification, or self-taught expertise</p>
                </div>



                <div>
                  <Label>Qualifications & Certifications *</Label>
                  <p className="text-sm text-gray-500 mb-2">List any relevant qualifications, certifications, or credentials that demonstrate your expertise</p>
                  {certifications.map((cert, index) => (
                    <div key={index} className="flex space-x-2 mt-2">
                      <Input
                        value={cert}
                        onChange={(e) => {
                          const newCerts = [...certifications]
                          newCerts[index] = e.target.value
                          setCertifications(newCerts)
                        }}
                        placeholder="e.g., TEFL Certificate, Teaching License, Subject-specific Certification, Professional Experience"
                        disabled={saving}
                      />
                      {index === certifications.length - 1 && (
                        <Button
                          type="button"
                          onClick={() => setCertifications([...certifications, ''])}
                          variant="outline"
                          size="sm"
                        >
                          Add
                        </Button>
                      )}
                    </div>
                  ))}
                </div>

                <div>
                  <Label>Languages</Label>
                  <p className="text-sm text-gray-500 mb-3">Select the languages you can teach in</p>

                  {/* Language Checkboxes */}
                  <div className="mb-4">
                    {/* Common Languages - Larger, more prominent */}
                    <div className="grid grid-cols-2 gap-3 mb-4">
                      {commonLanguages.map((language) => (
                        <label key={language} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={languages.includes(language)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setLanguages([...languages.filter(l => l.trim()), language])
                              } else {
                                setLanguages(languages.filter(l => l !== language))
                              }
                            }}
                            disabled={saving}
                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                          />
                          <span className="font-medium text-gray-900">{language}</span>
                        </label>
                      ))}
                    </div>

                    {/* Separator */}
                    <div className="relative mb-4">
                      <div className="absolute inset-0 flex items-center">
                        <div className="w-full border-t border-gray-200"></div>
                      </div>
                      <div className="relative flex justify-center text-sm">
                        <span className="px-2 bg-white text-gray-500">More Languages</span>
                      </div>
                    </div>

                    {/* Other Languages - Smaller, in dropdown style */}
                    <details className="border rounded-lg">
                      <summary className="p-3 cursor-pointer hover:bg-gray-50 font-medium text-gray-700">
                        Additional Languages ({otherLanguages.length} available)
                      </summary>
                      <div className="p-3 border-t bg-gray-50 grid grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                        {otherLanguages.map((language) => (
                          <label key={language} className="flex items-center space-x-2 text-sm">
                            <input
                              type="checkbox"
                              checked={languages.includes(language)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setLanguages([...languages.filter(l => l.trim()), language])
                                } else {
                                  setLanguages(languages.filter(l => l !== language))
                                }
                              }}
                              disabled={saving}
                              className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <span className="text-gray-700">{language}</span>
                          </label>
                        ))}
                      </div>
                    </details>
                  </div>

                  {/* Selected Languages Display */}
                  {languages.filter(l => l.trim()).length > 0 && (
                    <div className="mb-4">
                      <p className="text-sm font-medium text-gray-700 mb-2">Selected Languages:</p>
                      <div className="flex flex-wrap gap-2">
                        {languages.filter(l => l.trim()).map((language) => (
                          <span
                            key={language}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800"
                          >
                            {language}
                            <button
                              type="button"
                              onClick={() => setLanguages(languages.filter(l => l !== language))}
                              className="ml-2 text-blue-600 hover:text-blue-800"
                              disabled={saving}
                            >
                              ×
                            </button>
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>



                <Button
                  onClick={() => handleSave('tutor')}
                  disabled={saving}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {saving ? 'Saving...' : isTutor ? 'Update Profile' : 'Submit Application'}
                </Button>

                {!isTutor && (
                  <Button
                    onClick={() => setActiveSection('personal')}
                    variant="outline"
                    className="ml-4"
                  >
                    Cancel
                  </Button>
                )}
              </div>
            )}

            {activeSection === 'settings' && (
              <div className="space-y-6">
                <h2 className="text-lg font-semibold">Preferences</h2>
                <p className="text-gray-600">Notification and preference settings coming soon...</p>
              </div>
            )}

            {/* Apply to be Tutor section for non-tutors */}
            {!isTutor && activeSection === 'personal' && (
              <div className="mt-8 p-4 bg-blue-50 rounded-lg">
                <h3 className="text-lg font-semibold text-blue-900 mb-2">Become a Tutor</h3>
                <p className="text-blue-700 mb-4">Share your knowledge and earn money by teaching students.</p>
                <Button 
                  onClick={() => setActiveSection('tutor-apply')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Apply to Become a Tutor
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}

export default function AccountPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    }>
      <AccountPageContent />
    </Suspense>
  )
}
