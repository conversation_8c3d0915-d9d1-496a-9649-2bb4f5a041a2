'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { 
  Users, 
  GraduationCap, 
  DollarSign, 
  TrendingUp,
  Search,
  Filter,
  ChevronDown,
  Eye,
  Edit,
  MoreHorizontal,
  UserCheck,
  Clock,
  Star
} from 'lucide-react'

export default function AdminDesignProposalsPage() {
  const [selectedDesign, setSelectedDesign] = useState<'current' | 'proposed'>('current')
  const [proposedSubDesign, setProposedSubDesign] = useState<'modern' | 'credo'>('credo')
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<{ id: string; email: string; full_name?: string } | null>(null)
  
  const supabase = createClient()
  const router = useRouter()

  const checkAdminAccess = useCallback(async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError) {
        console.error('Auth error:', userError)
        router.push('/auth')
        return
      }

      if (!user) {
        router.push('/auth')
        return
      }

      // Check if user has admin role (multi-role system)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        console.error('Profile error:', profileError)
        router.push('/dashboard')
        return
      }

      if (!profile) {
        router.push('/dashboard')
        return
      }

      // Check if user has admin role in the roles array
      if (!profile.roles || !profile.roles.includes('admin') || profile.admin_status !== 'approved') {
        router.push('/dashboard')
        return
      }

      setUser({
        id: user.id,
        email: user.email || '',
        full_name: profile.full_name
      })
      
    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [supabase, router])

  useEffect(() => {
    checkAdminAccess()
  }, [checkAdminAccess])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🎨 Design System Proposals</h1>
          <p className="text-gray-600">Development page for design iterations - Admin only</p>
          
          {/* Toggle */}
          <div className="mt-4 flex space-x-2">
            <Button
              onClick={() => setSelectedDesign('current')}
              variant={selectedDesign === 'current' ? 'default' : 'outline'}
              size="sm"
            >
              Current Design
            </Button>
            <Button
              onClick={() => setSelectedDesign('proposed')}
              variant={selectedDesign === 'proposed' ? 'default' : 'outline'}
              size="sm"
            >
              Proposed Design
            </Button>
          </div>

          {/* Sub-design toggle for proposed */}
          {selectedDesign === 'proposed' && (
            <div className="mt-4 flex space-x-2">
              <Button
                onClick={() => setProposedSubDesign('modern')}
                variant={proposedSubDesign === 'modern' ? 'default' : 'outline'}
                size="sm"
              >
                Modern Layout
              </Button>
              <Button
                onClick={() => setProposedSubDesign('credo')}
                variant={proposedSubDesign === 'credo' ? 'default' : 'outline'}
                size="sm"
              >
                CREDO-Inspired
              </Button>
            </div>
          )}
        </div>

        {selectedDesign === 'current' && (
          <div className="space-y-8">
            <h2 className="text-2xl font-semibold text-gray-900">Current Design (Issues)</h2>
            
            {/* Current Admin Dashboard Replica */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-xl font-semibold">Admin Dashboard</h3>
                  <p className="text-gray-600">Monitor users and platform metrics</p>
                </div>
                <Button variant="outline" size="sm">Refresh Data</Button>
              </div>

              {/* Current Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold">22</p>
                      <p className="text-xs text-gray-500">+0 this month</p>
                    </div>
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Active Tutors</p>
                      <p className="text-2xl font-bold">15</p>
                      <p className="text-xs text-gray-500">0 pending approval</p>
                    </div>
                    <GraduationCap className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
              </div>

              {/* Current User Management */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold mb-4">User Management</h4>
                
                {/* Current Search & Filters */}
                <div className="flex space-x-4 mb-4">
                  <Input placeholder="Search users..." className="flex-1" />
                  <div className="relative">
                    <Button 
                      variant="outline" 
                      onClick={() => setDropdownOpen(!dropdownOpen)}
                      className="flex items-center space-x-2"
                    >
                      <span>All Roles</span>
                      <ChevronDown className="w-4 h-4" />
                    </Button>
                    {dropdownOpen && (
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-sm z-10 min-w-[120px]">
                        <div className="py-1">
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">All Roles</div>
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">Students</div>
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">Tutors</div>
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">Admins</div>
                        </div>
                      </div>
                    )}
                  </div>
                  <Button variant="outline">All Status</Button>
                </div>

                {/* Current Table */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">User</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Role</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Status</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Last Active</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                              TU
                            </div>
                            <div>
                              <p className="font-medium">Test User</p>
                              <p className="text-sm text-gray-500"><EMAIL></p>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <span className="px-2 py-1 text-xs rounded-full border border-gray-300 bg-white">Student</span>
                        </td>
                        <td className="px-4 py-3">
                          <span className="px-2 py-1 text-xs rounded-full border border-green-300 bg-green-50 text-green-700">Available</span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">9/20/2025</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Issues List */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-red-800 mb-3">❌ Current Design Issues:</h4>
              <ul className="space-y-2 text-red-700">
                <li>• Basic dropdown styling with poor visual hierarchy</li>
                <li>• Inconsistent button and badge styling</li>
                <li>• Plain metric cards with minimal visual interest</li>
                <li>• Generic gray color scheme lacks personality</li>
                <li>• Poor spacing and typography hierarchy</li>
                <li>• No hover states or micro-interactions</li>
              </ul>
            </div>
          </div>
        )}

        {selectedDesign === 'proposed' && (
          <div className="space-y-8">
            {proposedSubDesign === 'modern' && (
              <>
                <h2 className="text-2xl font-semibold text-gray-900">Hexagonal Design Proposal (Inspired by SERVICIOS)</h2>

            {/* Hexagonal Admin Dashboard */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8">
              <div className="flex justify-between items-center mb-12">
                <div>
                  <h3 className="text-3xl font-bold text-gray-900 tracking-wider">CONTROL CENTER</h3>
                  <p className="text-gray-600 mt-2 text-lg">Monitor users and platform metrics</p>
                </div>
                <Button className="bg-black hover:bg-gray-800 text-white px-6 py-3 text-sm tracking-wide">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  REFRESH DATA
                </Button>
              </div>

              {/* Hexagonal Metrics Layout */}
              <div className="flex justify-center mb-12">
                <div className="relative">
                  {/* Top Row */}
                  <div className="flex justify-center mb-4">
                    <div className="hexagon-container mx-4">
                      <div className="hexagon bg-black text-white hover:bg-gray-800 transition-all duration-300 cursor-pointer">
                        <div className="hexagon-content">
                          <Users className="w-8 h-8 mb-2" />
                          <div className="text-2xl font-bold">22</div>
                          <div className="text-xs opacity-80">TOTAL USERS</div>
                          <div className="text-xs opacity-60 mt-1">+0 this month</div>
                        </div>
                      </div>
                    </div>

                    <div className="hexagon-container mx-4">
                      <div className="hexagon bg-black text-white hover:bg-gray-800 transition-all duration-300 cursor-pointer">
                        <div className="hexagon-content">
                          <GraduationCap className="w-8 h-8 mb-2" />
                          <div className="text-2xl font-bold">15</div>
                          <div className="text-xs opacity-80">ACTIVE TUTORS</div>
                          <div className="text-xs opacity-60 mt-1">0 pending</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Bottom Row */}
                  <div className="flex justify-center">
                    <div className="hexagon-container mx-4">
                      <div className="hexagon bg-black text-white hover:bg-gray-800 transition-all duration-300 cursor-pointer">
                        <div className="hexagon-content">
                          <UserCheck className="w-8 h-8 mb-2" />
                          <div className="text-2xl font-bold">22</div>
                          <div className="text-xs opacity-80">STUDENTS</div>
                          <div className="text-xs opacity-60 mt-1">22 active</div>
                        </div>
                      </div>
                    </div>

                    <div className="hexagon-container mx-4">
                      <div className="hexagon bg-black text-white hover:bg-gray-800 transition-all duration-300 cursor-pointer">
                        <div className="hexagon-content">
                          <DollarSign className="w-8 h-8 mb-2" />
                          <div className="text-2xl font-bold">$0</div>
                          <div className="text-xs opacity-80">EARNINGS</div>
                          <div className="text-xs opacity-60 mt-1">Platform revenue</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Improvements List */}
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold text-gray-800 mb-3">✨ Hexagonal Design Features:</h4>
                <ul className="space-y-2 text-gray-700">
                  <li>• **Geometric hexagonal layout** inspired by premium design systems</li>
                  <li>• **Pure black and white** aesthetic with subtle hover effects</li>
                  <li>• **Typography emphasis** with tracking and proper spacing</li>
                  <li>• **Sophisticated visual hierarchy** without color noise</li>
                  <li>• **Honeycomb pattern** creates visual interest and premium feel</li>
                  <li>• **Scalable design system** that can extend to other pages</li>
                </ul>
              </div>
            </div>

            {/* CSS for Hexagons */}
            <style jsx>{`
              .hexagon-container {
                width: 160px;
                height: 140px;
                position: relative;
              }

              .hexagon {
                width: 160px;
                height: 140px;
                position: relative;
                margin: 0 auto;
                clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .hexagon-content {
                text-align: center;
                padding: 20px;
              }
            `}</style>
              </>
            )}

            {proposedSubDesign === 'credo' && (
              <>
                <h2 className="text-2xl font-semibold text-gray-900">CREDO-Inspired Dark Dashboard</h2>

                {/* CREDO-Style Dark Dashboard */}
                <div className="bg-black rounded-xl shadow-2xl border border-gray-800 p-8 relative overflow-hidden">
                  {/* Subtle background pattern */}
                  <div className="absolute inset-0 opacity-5">
                    <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
                  </div>

                  <div className="relative z-10">
                    <div className="flex justify-between items-start mb-12">
                      <div>
                        <h3 className="text-4xl font-light text-white tracking-tight mb-2">Control Center</h3>
                        <p className="text-gray-400 text-base font-light">Your gateway to seamless platform management</p>
                        <p className="text-gray-500 text-sm mt-1 max-w-md leading-relaxed">
                          Founded in 2024, our platform is creating global opportunities, building firm-like
                          digital asset spaces for tutors and students.
                        </p>
                      </div>
                      <Button className="bg-white hover:bg-gray-100 text-black px-4 py-2 text-xs font-medium tracking-wide border-0 rounded-sm">
                        Contact Us →
                      </Button>
                    </div>

                    {/* Main Metrics - CREDO Style - Side by Side */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
                      {/* Total Users */}
                      <div className="text-right">
                        <div className="text-6xl font-light text-white mb-2">22</div>
                        <div className="text-gray-400 text-sm font-medium">Total Users</div>
                      </div>

                      {/* Active Tutors */}
                      <div className="text-right">
                        <div className="text-6xl font-light text-white mb-2">15</div>
                        <div className="text-gray-400 text-sm font-medium">Active Tutors</div>
                      </div>

                      {/* Students */}
                      <div className="text-right">
                        <div className="text-6xl font-light text-white mb-2">22</div>
                        <div className="text-gray-400 text-sm font-medium">Students</div>
                      </div>

                      {/* Revenue */}
                      <div className="text-right">
                        <div className="text-6xl font-light text-white mb-2">$0</div>
                        <div className="text-gray-400 text-sm font-medium">Platform Revenue</div>
                      </div>
                    </div>

                    {/* Bottom Section with Description */}
                    <div className="flex justify-between items-end">
                      <div className="max-w-md">
                        <p className="text-gray-400 text-sm leading-relaxed mb-3">
                          Our proven track record and comprehensive approach to
                          tutoring makes us a trusted partner for investors seeking
                          opportunities in the dynamic education sector.
                        </p>
                        <Button className="bg-transparent border border-gray-600 text-gray-300 hover:bg-gray-800 px-4 py-2 text-xs tracking-wide">
                          View more →
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Features List */}
                  <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 mt-12 relative z-10">
                    <h4 className="text-lg font-medium text-white mb-3">✨ CREDO-Inspired Design Features:</h4>
                    <ul className="space-y-2 text-gray-300 text-sm">
                      <li>• **Dark sophisticated theme** with black background and white text</li>
                      <li>• **Massive typography** (8xl) for primary metrics like CREDO</li>
                      <li>• **Asymmetric layout** with strategic positioning</li>
                      <li>• **Subtle background patterns** for depth without distraction</li>
                      <li>• **Professional copy** with investment/business language</li>
                      <li>• **Minimal button styling** with clean borders</li>
                      <li>• **Strategic white space** and sophisticated hierarchy</li>
                    </ul>
                  </div>
                </div>
              </>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
