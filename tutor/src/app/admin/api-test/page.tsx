'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import AdminHeader from '@/components/admin-header'
import { 
  CheckCircle, 
  XCircle, 
  Clock, 
  Upload, 
  Database, 
  Server,
  User,
  Shield,
  FileText
} from 'lucide-react'

type TestResult = {
  name: string
  description: string
  status: 'pending' | 'running' | 'passed' | 'failed'
  result?: any
  error?: string
  duration?: number
}

type TestCategory = {
  name: string
  icon: React.ReactNode
  tests: TestResult[]
}

export default function AdminAPITestPage() {
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [testCategories, setTestCategories] = useState<TestCategory[]>([])
  const [runningAll, setRunningAll] = useState(false)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  
  const supabase = createClient()
  const router = useRouter()

  const checkAdminAccess = useCallback(async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        router.push('/auth')
        return
      }

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError || !profile?.roles?.includes('admin') || profile.admin_status !== 'approved') {
        router.push('/dashboard')
        return
      }

      setUser({ ...user, profile })
    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [supabase, router])

  useEffect(() => {
    checkAdminAccess()
    initializeTests()
  }, [checkAdminAccess])

  const initializeTests = () => {
    const categories: TestCategory[] = [
      {
        name: 'API Routes',
        icon: <Server className="w-5 h-5" />,
        tests: [
          {
            name: 'POST /api/upload',
            description: 'Test file upload with authentication and validation',
            status: 'pending'
          },
          {
            name: 'POST /api/upload-profile-picture', 
            description: 'Test profile picture upload with base64 fallback',
            status: 'pending'
          }
        ]
      },
      {
        name: 'RPC Functions',
        icon: <Database className="w-5 h-5" />,
        tests: [
          {
            name: 'add_user_role',
            description: 'Test adding roles to user profiles',
            status: 'pending'
          },
          {
            name: 'set_tutor_verification',
            description: 'Test setting tutor verification status',
            status: 'pending'
          },
          {
            name: 'get_user_role_status',
            description: 'Test retrieving user role status',
            status: 'pending'
          },
          {
            name: 'get_dashboard_stats',
            description: 'Test dashboard statistics calculation',
            status: 'pending'
          },
          {
            name: 'complete_intake',
            description: 'Test intake completion workflow',
            status: 'pending'
          }
        ]
      },
      {
        name: 'Database Operations',
        icon: <FileText className="w-5 h-5" />,
        tests: [
          {
            name: 'Database Schema Check',
            description: 'Verify database schema and enum types',
            status: 'pending'
          },
          {
            name: 'Profile CRUD',
            description: 'Test profile create, read, update operations',
            status: 'pending'
          },
          {
            name: 'Tutor Filtering',
            description: 'Test tutor search and filtering functionality',
            status: 'pending'
          },
          {
            name: 'Authentication State',
            description: 'Test user authentication and session management',
            status: 'pending'
          }
        ]
      },
      {
        name: 'Security & Validation',
        icon: <Shield className="w-5 h-5" />,
        tests: [
          {
            name: 'Unauthorized Access',
            description: 'Test API protection against unauthorized requests',
            status: 'pending'
          },
          {
            name: 'File Validation',
            description: 'Test file type and size validation',
            status: 'pending'
          },
          {
            name: 'SQL Injection Protection',
            description: 'Test database query protection',
            status: 'pending'
          }
        ]
      }
    ]
    
    setTestCategories(categories)
  }

  const updateTestResult = (categoryIndex: number, testIndex: number, updates: Partial<TestResult>) => {
    setTestCategories(prev => {
      const newCategories = [...prev]
      newCategories[categoryIndex].tests[testIndex] = {
        ...newCategories[categoryIndex].tests[testIndex],
        ...updates
      }
      return newCategories
    })
  }

  const runTest = async (categoryIndex: number, testIndex: number) => {
    const test = testCategories[categoryIndex].tests[testIndex]
    const startTime = Date.now()
    
    updateTestResult(categoryIndex, testIndex, { status: 'running' })

    try {
      let result: any = null

      switch (test.name) {
        case 'POST /api/upload':
          result = await testFileUpload()
          break
        case 'POST /api/upload-profile-picture':
          result = await testProfilePictureUpload()
          break
        case 'add_user_role':
          result = await testAddUserRole()
          break
        case 'set_tutor_verification':
          result = await testSetTutorVerification()
          break
        case 'get_user_role_status':
          result = await testGetUserRoleStatus()
          break
        case 'get_dashboard_stats':
          result = await testGetDashboardStats()
          break
        case 'complete_intake':
          result = await testCompleteIntake()
          break
        case 'Database Schema Check':
          result = await testDatabaseSchema()
          break
        case 'Profile CRUD':
          result = await testProfileCRUD()
          break
        case 'Tutor Filtering':
          result = await testTutorFiltering()
          break
        case 'Authentication State':
          result = await testAuthenticationState()
          break
        case 'Unauthorized Access':
          result = await testUnauthorizedAccess()
          break
        case 'File Validation':
          result = await testFileValidation()
          break
        case 'SQL Injection Protection':
          result = await testSQLInjectionProtection()
          break
        default:
          throw new Error('Test not implemented')
      }

      const duration = Date.now() - startTime
      updateTestResult(categoryIndex, testIndex, {
        status: 'passed',
        result,
        duration
      })
    } catch (error) {
      const duration = Date.now() - startTime
      updateTestResult(categoryIndex, testIndex, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        duration
      })
    }
  }

  // Test implementations will be added in the next part
  const testFileUpload = async () => {
    if (!selectedFile) {
      throw new Error('No file selected for upload test')
    }

    const formData = new FormData()
    formData.append('file', selectedFile)
    formData.append('type', 'profile')

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Upload failed')
    }

    return await response.json()
  }

  const testProfilePictureUpload = async () => {
    if (!selectedFile || !user) {
      throw new Error('No file selected or user not authenticated')
    }

    const formData = new FormData()
    formData.append('file', selectedFile)
    formData.append('userId', user.id)

    const response = await fetch('/api/upload-profile-picture', {
      method: 'POST',
      body: formData
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Profile picture upload failed')
    }

    return await response.json()
  }

  const testAddUserRole = async () => {
    if (!user) throw new Error('User not authenticated')

    const { data, error } = await supabase.rpc('add_user_role', {
      user_id: user.id,
      new_role: 'student'
    })

    if (error) throw error
    return { success: true, data }
  }

  const testSetTutorVerification = async () => {
    // Find a tutor to test with
    const { data: tutors, error: fetchError } = await supabase
      .from('profiles')
      .select('id, full_name, tutor_status, roles')
      .contains('roles', ['tutor'])
      .limit(1)

    if (fetchError) {
      throw new Error(`Failed to fetch tutors: ${fetchError.message}`)
    }
    if (!tutors || tutors.length === 0) {
      throw new Error('No tutors found to test with')
    }

    const tutorId = tutors[0].id
    const originalStatus = tutors[0].tutor_status

    try {
      // Test setting verification to true
      // Use explicit parameter names to avoid function signature ambiguity
      const { data, error } = await supabase.rpc('set_tutor_verification', {
        tutor_id: tutorId,
        verified: true
      })

      if (error) {
        // Check if it's the function signature conflict error
        if (error.code === 'PGRST203') {
          throw new Error(`Function signature conflict detected. Multiple versions of set_tutor_verification exist in database. Run fix_set_tutor_verification_conflict.sql to resolve. Original error: ${error.message}`)
        }
        throw new Error(`RPC call failed: ${error.message} (Code: ${error.code}, Details: ${error.details})`)
      }

      // Verify the change was applied
      const { data: updatedTutor, error: verifyError } = await supabase
        .from('profiles')
        .select('tutor_status')
        .eq('id', tutorId)
        .single()

      if (verifyError) {
        throw new Error(`Verification check failed: ${verifyError.message}`)
      }

      return {
        success: true,
        tutorId,
        originalStatus,
        newStatus: updatedTutor.tutor_status,
        data,
        tutorInfo: tutors[0]
      }
    } catch (error) {
      // Provide more detailed error information
      throw new Error(`Tutor verification test failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  const testGetUserRoleStatus = async () => {
    if (!user) throw new Error('User not authenticated')

    const { data, error } = await supabase.rpc('get_user_role_status', {
      user_id: user.id
    })

    if (error) throw error
    return { success: true, roles: data }
  }

  const testGetDashboardStats = async () => {
    const { data, error } = await supabase.rpc('get_dashboard_stats')

    if (error) throw error
    return { success: true, stats: data }
  }

  const testCompleteIntake = async () => {
    if (!user) throw new Error('User not authenticated')

    // Find a user to test intake completion with
    const { data: profiles, error: fetchError } = await supabase
      .from('profiles')
      .select('id')
      .is('intake_completed_at', null)
      .limit(1)

    if (fetchError) throw fetchError
    if (!profiles || profiles.length === 0) {
      return { success: true, message: 'No profiles available for intake completion test' }
    }

    const { data, error } = await supabase.rpc('complete_intake', {
      p_profile_id: profiles[0].id,
      p_completed_by: user.id,
      p_notes: 'API Test completion'
    })

    if (error) throw error
    return { success: true, profileId: profiles[0].id, data }
  }

  const testDatabaseSchema = async () => {
    // Check if approval_status enum exists and has correct values
    const { data: enumData, error: enumError } = await supabase
      .from('pg_enum')
      .select('enumlabel')
      .eq('enumtypid', 'approval_status')

    // Check profiles table structure
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, roles, tutor_status, student_status, admin_status')
      .limit(1)

    // Check if we have any tutors
    const { data: tutors, error: tutorsError } = await supabase
      .from('profiles')
      .select('id, full_name, tutor_status, roles')
      .contains('roles', ['tutor'])
      .limit(5)

    // Check if RPC functions exist by trying to get their definitions
    const { data: functions, error: functionsError } = await supabase
      .rpc('get_dashboard_stats')

    return {
      success: true,
      schema: {
        enumError: enumError?.message || null,
        profilesError: profilesError?.message || null,
        tutorsError: tutorsError?.message || null,
        functionsError: functionsError?.message || null,
        tutorCount: tutors?.length || 0,
        sampleTutor: tutors?.[0] || null,
        hasProfiles: !!profiles && profiles.length > 0
      }
    }
  }

  const testProfileCRUD = async () => {
    if (!user) throw new Error('User not authenticated')

    // Test READ
    const { data: profile, error: readError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (readError) throw new Error(`Read failed: ${readError.message}`)

    // Test UPDATE
    const testBio = `API Test Bio - ${Date.now()}`
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ bio: testBio })
      .eq('id', user.id)

    if (updateError) throw new Error(`Update failed: ${updateError.message}`)

    // Verify update
    const { data: updatedProfile, error: verifyError } = await supabase
      .from('profiles')
      .select('bio')
      .eq('id', user.id)
      .single()

    if (verifyError) throw new Error(`Verify failed: ${verifyError.message}`)
    if (updatedProfile.bio !== testBio) throw new Error('Update verification failed')

    return {
      success: true,
      operations: ['read', 'update', 'verify'],
      originalBio: profile.bio,
      updatedBio: testBio
    }
  }

  const testTutorFiltering = async () => {
    // Test basic tutor fetch
    const { data: allTutors, error: allError } = await supabase
      .from('profiles')
      .select('id, full_name, subjects, tutor_tier')
      .contains('roles', ['tutor'])
      .eq('tutor_status', 'approved')

    if (allError) throw new Error(`All tutors fetch failed: ${allError.message}`)

    // Test filtered fetch
    const { data: filteredTutors, error: filterError } = await supabase
      .from('profiles')
      .select('id, full_name, subjects')
      .contains('roles', ['tutor'])
      .eq('tutor_status', 'approved')
      .not('subjects', 'is', null)
      .limit(5)

    if (filterError) throw new Error(`Filtered fetch failed: ${filterError.message}`)

    return {
      success: true,
      totalTutors: allTutors?.length || 0,
      filteredTutors: filteredTutors?.length || 0,
      sampleTutor: filteredTutors?.[0] || null
    }
  }

  const testAuthenticationState = async () => {
    // Test current user
    const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser()
    if (userError) throw new Error(`Auth user failed: ${userError.message}`)

    // Test session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    if (sessionError) throw new Error(`Session failed: ${sessionError.message}`)

    return {
      success: true,
      hasUser: !!currentUser,
      hasSession: !!session,
      userId: currentUser?.id,
      sessionExpiry: session?.expires_at
    }
  }

  const testUnauthorizedAccess = async () => {
    // Create a new supabase client without authentication
    const anonSupabase = createClient()

    // Try to access admin-only function
    const { data, error } = await anonSupabase.rpc('get_dashboard_stats')

    // This should fail for non-admin users
    if (error) {
      return {
        success: true,
        message: 'Unauthorized access properly blocked',
        error: error.message
      }
    }

    // If it succeeds, that might be a security issue
    return {
      success: false,
      message: 'WARNING: Unauthorized access was allowed',
      data
    }
  }

  const testFileValidation = async () => {
    // Test with invalid file type
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' })
    const formData = new FormData()
    formData.append('file', invalidFile)

    try {
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        throw new Error('Invalid file type was accepted')
      }

      const error = await response.json()
      return {
        success: true,
        message: 'File validation working correctly',
        rejectedFileType: 'text/plain',
        errorMessage: error.error
      }
    } catch (error) {
      if (error instanceof Error && error.message === 'Invalid file type was accepted') {
        throw error
      }
      throw new Error(`File validation test failed: ${error}`)
    }
  }

  const testSQLInjectionProtection = async () => {
    // Test with malicious input
    const maliciousInput = "'; DROP TABLE profiles; --"

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .ilike('full_name', `%${maliciousInput}%`)
        .limit(1)

      // If no error, the query was safely handled
      return {
        success: true,
        message: 'SQL injection protection working',
        maliciousInput,
        queryExecuted: true,
        resultCount: data?.length || 0
      }
    } catch (error) {
      throw new Error(`SQL injection test failed: ${error}`)
    }
  }

  const runAllTests = async () => {
    setRunningAll(true)

    for (let categoryIndex = 0; categoryIndex < testCategories.length; categoryIndex++) {
      const category = testCategories[categoryIndex]
      for (let testIndex = 0; testIndex < category.tests.length; testIndex++) {
        await runTest(categoryIndex, testIndex)
        // Small delay between tests
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    setRunningAll(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading API Test Suite...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminHeader 
        title="API Test Suite"
        subtitle="Comprehensive testing of all API routes and server actions"
      />
      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5" />
              Test Controls
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Test File (for upload tests)</label>
              <Input
                type="file"
                accept="image/*"
                onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
              />
            </div>
            <div className="flex gap-2">
              <Button
                onClick={runAllTests}
                disabled={runningAll}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {runningAll ? <Clock className="w-4 h-4 mr-2 animate-spin" /> : null}
                Run All Tests
              </Button>
              <Button variant="outline" onClick={initializeTests}>
                Reset Tests
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Test Categories */}
        {testCategories.map((category, categoryIndex) => (
          <Card key={category.name}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {category.icon}
                {category.name}
                <Badge variant="outline">
                  {category.tests.filter(t => t.status === 'passed').length}/{category.tests.length} Passed
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {category.tests.map((test, testIndex) => (
                  <div key={test.name} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{test.name}</span>
                        {test.status === 'passed' && <CheckCircle className="w-4 h-4 text-green-500" />}
                        {test.status === 'failed' && <XCircle className="w-4 h-4 text-red-500" />}
                        {test.status === 'running' && <Clock className="w-4 h-4 text-blue-500 animate-spin" />}
                        {test.duration && (
                          <Badge variant="secondary" className="text-xs">
                            {test.duration}ms
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 mt-1">{test.description}</p>
                      {test.error && (
                        <p className="text-sm text-red-600 mt-1 font-mono bg-red-50 p-2 rounded">
                          Error: {test.error}
                        </p>
                      )}
                      {test.result && test.status === 'passed' && (
                        <details className="mt-2">
                          <summary className="text-sm text-green-600 cursor-pointer">View Result</summary>
                          <pre className="text-xs bg-green-50 p-2 rounded mt-1 overflow-auto">
                            {JSON.stringify(test.result, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => runTest(categoryIndex, testIndex)}
                      disabled={test.status === 'running'}
                    >
                      {test.status === 'running' ? 'Running...' : 'Run Test'}
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
