'use client'

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@/lib/supabase/client'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Users,
  GraduationCap,
  DollarSign,
  TrendingUp,
  Search,
  Filter,
  ChevronDown,
  Eye,
  Edit,
  MoreHorizontal,
  UserCheck,
  Clock,
  Star
} from 'lucide-react'

export default function AdminDesignDevPage() {
  const [selectedDesign, setSelectedDesign] = useState<'current' | 'proposed'>('current')
  const [dropdownOpen, setDropdownOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<{ id: string; email: string; full_name?: string } | null>(null)

  const supabase = createClient()
  const router = useRouter()

  const checkAdminAccess = useCallback(async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()

      if (userError) {
        console.error('Auth error:', userError)
        router.push('/auth')
        return
      }

      if (!user) {
        router.push('/auth')
        return
      }

      // Check if user has admin role (multi-role system)
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        console.error('Profile error:', profileError)
        router.push('/dashboard')
        return
      }

      if (!profile) {
        router.push('/dashboard')
        return
      }

      // Check if user has admin role in the roles array
      if (!profile.roles || !profile.roles.includes('admin') || profile.admin_status !== 'approved') {
        router.push('/dashboard')
        return
      }

      setUser({
        id: user.id,
        email: user.email || '',
        full_name: profile.full_name
      })

    } catch (error) {
      console.error('Error checking admin access:', error)
      router.push('/dashboard')
    } finally {
      setLoading(false)
    }
  }, [supabase, router])

  useEffect(() => {
    checkAdminAccess()
  }, [checkAdminAccess])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect via useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">🎨 Design System Proposals</h1>
          <p className="text-gray-600">Development page for design iterations - Not for users</p>
          
          {/* Toggle */}
          <div className="mt-4 flex space-x-2">
            <Button
              onClick={() => setSelectedDesign('current')}
              variant={selectedDesign === 'current' ? 'default' : 'outline'}
              size="sm"
            >
              Current Design
            </Button>
            <Button
              onClick={() => setSelectedDesign('proposed')}
              variant={selectedDesign === 'proposed' ? 'default' : 'outline'}
              size="sm"
            >
              Proposed Design
            </Button>
          </div>
        </div>

        {selectedDesign === 'current' && (
          <div className="space-y-8">
            <h2 className="text-2xl font-semibold text-gray-900">Current Design (Issues)</h2>
            
            {/* Current Admin Dashboard Replica */}
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-center mb-6">
                <div>
                  <h3 className="text-xl font-semibold">Admin Dashboard</h3>
                  <p className="text-gray-600">Monitor users and platform metrics</p>
                </div>
                <Button variant="outline" size="sm">Refresh Data</Button>
              </div>

              {/* Current Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold">22</p>
                      <p className="text-xs text-gray-500">+0 this month</p>
                    </div>
                    <Users className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
                
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-gray-600">Active Tutors</p>
                      <p className="text-2xl font-bold">15</p>
                      <p className="text-xs text-gray-500">0 pending approval</p>
                    </div>
                    <GraduationCap className="w-5 h-5 text-gray-400" />
                  </div>
                </div>
              </div>

              {/* Current User Management */}
              <div className="border border-gray-200 rounded-lg p-6">
                <h4 className="text-lg font-semibold mb-4">User Management</h4>
                
                {/* Current Search & Filters */}
                <div className="flex space-x-4 mb-4">
                  <Input placeholder="Search users..." className="flex-1" />
                  <div className="relative">
                    <Button 
                      variant="outline" 
                      onClick={() => setDropdownOpen(!dropdownOpen)}
                      className="flex items-center space-x-2"
                    >
                      <span>All Roles</span>
                      <ChevronDown className="w-4 h-4" />
                    </Button>
                    {dropdownOpen && (
                      <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-sm z-10 min-w-[120px]">
                        <div className="py-1">
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">All Roles</div>
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">Students</div>
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">Tutors</div>
                          <div className="px-3 py-2 text-sm hover:bg-gray-50 cursor-pointer">Admins</div>
                        </div>
                      </div>
                    )}
                  </div>
                  <Button variant="outline">All Status</Button>
                </div>

                {/* Current Table */}
                <div className="border border-gray-200 rounded-lg overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">User</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Role</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Status</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-900">Last Active</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200">
                      <tr>
                        <td className="px-4 py-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                              TU
                            </div>
                            <div>
                              <p className="font-medium">Test User</p>
                              <p className="text-sm text-gray-500"><EMAIL></p>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <span className="px-2 py-1 text-xs rounded-full border border-gray-300 bg-white">Student</span>
                        </td>
                        <td className="px-4 py-3">
                          <span className="px-2 py-1 text-xs rounded-full border border-green-300 bg-green-50 text-green-700">Available</span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500">9/20/2025</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Issues List */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-red-800 mb-3">❌ Current Design Issues:</h4>
              <ul className="space-y-2 text-red-700">
                <li>• Basic dropdown styling with poor visual hierarchy</li>
                <li>• Inconsistent button and badge styling</li>
                <li>• Plain metric cards with minimal visual interest</li>
                <li>• Generic gray color scheme lacks personality</li>
                <li>• Poor spacing and typography hierarchy</li>
                <li>• No hover states or micro-interactions</li>
              </ul>
            </div>
          </div>
        )}

        {selectedDesign === 'proposed' && (
          <div className="space-y-8">
            <h2 className="text-2xl font-semibold text-gray-900">Proposed Modern Design</h2>
            
            {/* Proposed Admin Dashboard */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
              <div className="flex justify-between items-center mb-8">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">Control Center</h3>
                  <p className="text-gray-600 mt-1">Monitor users and platform metrics</p>
                </div>
                <Button className="bg-gray-900 hover:bg-gray-800 text-white shadow-sm">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Refresh Data
                </Button>
              </div>

              {/* Proposed Metrics Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-xl p-6 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                      <Users className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-3xl font-bold text-gray-900">22</p>
                      <p className="text-sm text-blue-600 font-medium">+0 this month</p>
                    </div>
                  </div>
                  <p className="text-sm font-medium text-gray-700">Total Users</p>
                </div>
                
                <div className="bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-xl p-6 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                      <GraduationCap className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-3xl font-bold text-gray-900">15</p>
                      <p className="text-sm text-green-600 font-medium">0 pending</p>
                    </div>
                  </div>
                  <p className="text-sm font-medium text-gray-700">Active Tutors</p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-xl p-6 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                      <UserCheck className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-3xl font-bold text-gray-900">22</p>
                      <p className="text-sm text-purple-600 font-medium">22 active</p>
                    </div>
                  </div>
                  <p className="text-sm font-medium text-gray-700">Students</p>
                </div>

                <div className="bg-gradient-to-br from-amber-50 to-amber-100 border border-amber-200 rounded-xl p-6 hover:shadow-md transition-all duration-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="w-12 h-12 bg-amber-500 rounded-lg flex items-center justify-center">
                      <DollarSign className="w-6 h-6 text-white" />
                    </div>
                    <div className="text-right">
                      <p className="text-3xl font-bold text-gray-900">$0</p>
                      <p className="text-sm text-amber-600 font-medium">Platform revenue</p>
                    </div>
                  </div>
                  <p className="text-sm font-medium text-gray-700">Total Earnings</p>
                </div>
              </div>

              {/* Proposed User Management */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h4 className="text-xl font-bold text-gray-900 mb-6">User Management</h4>
                
                {/* Proposed Search & Filters */}
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <Input 
                      placeholder="Search users..." 
                      className="pl-10 bg-white border-gray-200 focus:border-blue-500 focus:ring-blue-500" 
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <div className="relative">
                      <Button 
                        variant="outline" 
                        className="bg-white border-gray-200 hover:bg-gray-50 shadow-sm"
                        onClick={() => setDropdownOpen(!dropdownOpen)}
                      >
                        <Filter className="w-4 h-4 mr-2" />
                        All Roles
                        <ChevronDown className="w-4 h-4 ml-2" />
                      </Button>
                      {dropdownOpen && (
                        <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[160px]">
                          <div className="py-2">
                            <div className="px-4 py-2 text-sm hover:bg-blue-50 hover:text-blue-700 cursor-pointer rounded-md mx-2 transition-colors">
                              <Users className="w-4 h-4 inline mr-2" />
                              All Roles
                            </div>
                            <div className="px-4 py-2 text-sm hover:bg-blue-50 hover:text-blue-700 cursor-pointer rounded-md mx-2 transition-colors">
                              <UserCheck className="w-4 h-4 inline mr-2" />
                              Students
                            </div>
                            <div className="px-4 py-2 text-sm hover:bg-green-50 hover:text-green-700 cursor-pointer rounded-md mx-2 transition-colors">
                              <GraduationCap className="w-4 h-4 inline mr-2" />
                              Tutors
                            </div>
                            <div className="px-4 py-2 text-sm hover:bg-purple-50 hover:text-purple-700 cursor-pointer rounded-md mx-2 transition-colors">
                              <Star className="w-4 h-4 inline mr-2" />
                              Admins
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                    
                    <Button variant="outline" className="bg-white border-gray-200 hover:bg-gray-50 shadow-sm">
                      <Clock className="w-4 h-4 mr-2" />
                      All Status
                    </Button>
                  </div>
                </div>

                {/* Proposed Table */}
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                  <table className="w-full">
                    <thead className="bg-gray-50 border-b border-gray-200">
                      <tr>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">User</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Role</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Status</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Last Active</th>
                        <th className="px-6 py-4 text-left text-sm font-semibold text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      <tr className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-semibold shadow-sm">
                              TU
                            </div>
                            <div>
                              <p className="font-semibold text-gray-900">Test User</p>
                              <p className="text-sm text-gray-500"><EMAIL></p>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                            <UserCheck className="w-3 h-3 mr-1" />
                            Student
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            Available
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600 font-medium">9/20/2025</td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-700">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                      <tr className="hover:bg-gray-50 transition-colors">
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center text-white font-semibold shadow-sm">
                              T5
                            </div>
                            <div>
                              <p className="font-semibold text-gray-900">Tutor5 Test</p>
                              <p className="text-sm text-gray-500"><EMAIL></p>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            <GraduationCap className="w-3 h-3 mr-1" />
                            Tutor
                          </span>
                        </td>
                        <td className="px-6 py-4">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                            Available
                          </span>
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-600 font-medium">9/20/2025</td>
                        <td className="px-6 py-4">
                          <div className="flex items-center space-x-2">
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-700">
                              <Eye className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                              <Edit className="w-4 h-4" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Improvements List */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-green-800 mb-3">✅ Proposed Design Improvements:</h4>
              <ul className="space-y-2 text-green-700">
                <li>• Modern gradient cards with hover effects and better visual hierarchy</li>
                <li>• Semantic color system (blue=students, green=tutors, purple=admins)</li>
                <li>• Enhanced dropdowns with icons and hover states</li>
                <li>• Better typography with proper font weights and spacing</li>
                <li>• Micro-interactions and smooth transitions</li>
                <li>• Improved action buttons with clear visual feedback</li>
                <li>• "Control Center" instead of generic "Dashboard" naming</li>
              </ul>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
