"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { createClient } from "@/lib/supabase/client"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Users, UserCheck, GraduationCap, Shield, TrendingUp, Calendar, DollarSign, Star } from "lucide-react"
import AdminHeader from "@/components/admin-header"
import Avatar from "@/components/avatar"

type UserProfile = {
  id: string
  email: string
  full_name: string
  roles: string[]
  student_status: string | null
  tutor_status: string | null
  admin_status: string | null
  created_at: string
  last_activity_at: string | null
  hourly_rate: number | null
  total_sessions_taught: number | null
  average_rating: number | null
  total_earnings_cents: number | null
  phone_number: string | null
  subjects: string[] | null
  experience_years: number | null
  is_available: boolean | null
  approved_at: string | null
  profile_picture_url: string | null
  avatar_url: string | null
}

type DashboardStats = {
  total_users: number
  total_students: number
  total_tutors: number
  total_admins: number
  new_this_month: number
  active_this_week: number
  pending_approvals: number
  total_earnings: number
}

export default function AdminDashboardPage() {
  const router = useRouter()
  const supabase = createClient()
  
  const [loading, setLoading] = useState(true)
  const [users, setUsers] = useState<UserProfile[]>([])
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [search, setSearch] = useState("")
  const [roleFilter, setRoleFilter] = useState<string>("all")
  const [statusFilter, setStatusFilter] = useState<string>("all")

  useEffect(() => {
    const init = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        if (!user) {
          router.push("/auth")
          return
        }

        const { data: profile } = await supabase.from("profiles").select("roles").eq("id", user.id).single()
        if (!profile?.roles?.includes("admin")) {
          router.push("/dashboard")
          return
        }

        await Promise.all([loadUsers(), loadStats()])
      } finally {
        setLoading(false)
      }
    }
    init()
  }, [])

  const loadUsers = async () => {
    const { data } = await supabase
      .from("profiles")
      .select(`
        id, email, full_name, roles, student_status, tutor_status, admin_status,
        created_at, last_activity_at, hourly_rate, total_sessions_taught,
        average_rating, total_earnings_cents, phone_number, subjects,
        experience_years, is_available, approved_at, profile_picture_url, avatar_url
      `)
      .is("deleted_at", null)
      .order("created_at", { ascending: false })
    
    setUsers(data || [])
  }

  const loadStats = async () => {
    // Get basic counts and stats
    const { data: basicStats } = await supabase.rpc('get_dashboard_stats')
    
    if (basicStats) {
      setStats(basicStats)
    } else {
      // Fallback: calculate stats manually
      const { data: allUsers } = await supabase
        .from("profiles")
        .select("roles, created_at, last_activity_at, total_earnings_cents, tutor_status, student_status")
        .is("deleted_at", null)
      
      if (allUsers) {
        const now = new Date()
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        
        setStats({
          total_users: allUsers.length,
          total_students: allUsers.filter(u => u.roles?.includes('student')).length,
          total_tutors: allUsers.filter(u => u.roles?.includes('tutor')).length,
          total_admins: allUsers.filter(u => u.roles?.includes('admin')).length,
          new_this_month: allUsers.filter(u => new Date(u.created_at) > monthAgo).length,
          active_this_week: allUsers.filter(u => u.last_activity_at && new Date(u.last_activity_at) > weekAgo).length,
          pending_approvals: allUsers.filter(u => u.tutor_status === 'pending' || u.student_status === 'pending').length,
          total_earnings: allUsers.reduce((sum, u) => sum + (u.total_earnings_cents || 0), 0)
        })
      }
    }
  }

  const filteredUsers = users.filter(user => {
    const matchesSearch = search === "" || 
      user.full_name.toLowerCase().includes(search.toLowerCase()) ||
      user.email.toLowerCase().includes(search.toLowerCase()) ||
      (user.phone_number && user.phone_number.includes(search))
    
    const matchesRole = roleFilter === "all" || user.roles?.includes(roleFilter)
    
    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "pending" && (user.tutor_status === "pending" || user.student_status === "pending")) ||
      (statusFilter === "approved" && (user.tutor_status === "approved" || user.student_status === "approved")) ||
      (statusFilter === "active" && user.last_activity_at && new Date(user.last_activity_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000))
    
    return matchesSearch && matchesRole && matchesStatus
  })

  const formatCurrency = (cents: number | null) => {
    if (!cents) return "$0"
    return `$${(cents / 100).toFixed(2)}`
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Never"
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadge = (user: UserProfile) => {
    const roles = user.roles || []
    if (roles.includes('admin')) return <Badge variant="destructive">Admin</Badge>
    if (roles.includes('tutor') && user.tutor_status === 'approved') return <Badge variant="default">Tutor</Badge>
    if (roles.includes('tutor') && user.tutor_status === 'pending') return <Badge variant="secondary">Tutor (Pending)</Badge>
    if (roles.includes('student')) return <Badge variant="outline">Student</Badge>
    return <Badge variant="secondary">User</Badge>
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminHeader
        title="Admin Dashboard"
        subtitle="Platform management and analytics"
      />
      <div className="max-w-7xl mx-auto p-6 space-y-6">

        {/* CREDO-Inspired Dark Dashboard */}
        {stats && (
          <div className="bg-black rounded-xl shadow-2xl border border-gray-800 p-6 relative overflow-hidden">
            {/* Subtle background pattern */}
            <div className="absolute inset-0 opacity-5">
              <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-transparent via-white/10 to-transparent"></div>
            </div>

            <div className="relative z-10">
              <div className="flex justify-between items-center mb-8">
                <div>
                  <h3 className="text-3xl font-light text-white tracking-tight">Control Center</h3>
                </div>
                <Button
                  onClick={loadUsers}
                  className="bg-white hover:bg-gray-100 text-black px-4 py-2 text-xs font-medium tracking-wide border-0 rounded-sm"
                >
                  <TrendingUp className="w-3 h-3 mr-2" />
                  Refresh Data
                </Button>
              </div>

              {/* Main Metrics - CREDO Style - Side by Side */}
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
                {/* Total Users */}
                <div className="text-right">
                  <div className="text-5xl font-light text-white mb-2">{stats.total_users}</div>
                  <div className="text-gray-400 text-sm font-medium">Total Users</div>
                  <div className="text-gray-500 text-xs mt-1">+{stats.new_this_month} this month</div>
                </div>

                {/* Active Tutors */}
                <div className="text-right">
                  <div className="text-5xl font-light text-white mb-2">{stats.total_tutors}</div>
                  <div className="text-gray-400 text-sm font-medium">Active Tutors</div>
                  <div className="text-gray-500 text-xs mt-1">{stats.pending_approvals} pending</div>
                </div>

                {/* Students */}
                <div className="text-right">
                  <div className="text-5xl font-light text-white mb-2">{stats.total_students}</div>
                  <div className="text-gray-400 text-sm font-medium">Students</div>
                  <div className="text-gray-500 text-xs mt-1">{stats.active_this_week} active</div>
                </div>

                {/* Revenue */}
                <div className="text-right">
                  <div className="text-5xl font-light text-white mb-2">{formatCurrency(stats.total_earnings)}</div>
                  <div className="text-gray-400 text-sm font-medium">Platform Revenue</div>
                  <div className="text-gray-500 text-xs mt-1">Total earnings</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle>User Management</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <Input
                placeholder="Search users..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                className="max-w-sm"
              />
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="student">Students</SelectItem>
                  <SelectItem value="tutor">Tutors</SelectItem>
                  <SelectItem value="admin">Admins</SelectItem>
                </SelectContent>
              </Select>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="active">Active This Week</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Users Table */}
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Joined</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead>Rate</TableHead>
                    <TableHead>Sessions</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar
                            src={user.profile_picture_url || user.avatar_url}
                            name={user.full_name}
                            id={user.id}
                            size="sm"
                          />
                          <div>
                            <div className="font-medium">{user.full_name}</div>
                            <div className="text-sm text-muted-foreground">{user.email}</div>
                            {user.phone_number && (
                              <div className="text-xs text-muted-foreground">{user.phone_number}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getStatusBadge(user)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {user.is_available && <Badge variant="outline" className="text-xs">Available</Badge>}
                          {user.average_rating && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-xs">{user.average_rating.toFixed(1)}</span>
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{formatDate(user.created_at)}</TableCell>
                      <TableCell>{formatDate(user.last_activity_at)}</TableCell>
                      <TableCell>{user.hourly_rate ? formatCurrency(user.hourly_rate) : "-"}</TableCell>
                      <TableCell>{user.total_sessions_taught || 0}</TableCell>
                      <TableCell>
                        <Button variant="outline" size="sm">View</Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {filteredUsers.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No users found matching your filters.
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
