import { Button } from '@/components/ui/button'
import Logo from '@/components/logo'

export default function ProTutorLandingPage() {
  return (
    <div className="h-screen bg-white flex flex-col">
      {/* Navigation */}
      <header className="bg-white w-full h-16 flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 h-full flex justify-between items-center">
          <Logo href="/landingpage" size="md" />
        </div>
      </header>

      {/* Hero Section - 3 Parts */}
      <section className="bg-black text-white flex-1 flex items-center">
        <div className="w-full flex ml-32">
          {/* Part 1: Hero Copy - More space */}
          <div className="w-1/3 pl-4 pr-8 space-y-10">
            <div className="space-y-6">
              <p className="text-gray-300 text-2xl font-medium">Tu<PERSON> on a trusted platform.</p>
              <h1 className="text-7xl font-bold leading-tight tracking-tight">
                Powerful tutoring and classes.
              </h1>
            </div>

            <p className="text-2xl text-white leading-relaxed">
              Set your own rate, we&apos;ll connect you with students, you earn money on your schedule.
            </p>

            <div className="pt-6">
              <Button className="bg-white text-black hover:bg-gray-100 text-xl px-10 py-4" style={{fontFamily: 'Framer, -apple-system, BlinkMacSystemFont, sans-serif'}}>
                Get started today
              </Button>
            </div>
          </div>

          {/* Part 2: Two Images Stacked - Unaligned */}
          <div className="w-1/4 px-6 relative h-full">
            {/* Top Image - Higher position, smaller */}
            <div className="bg-gray-800 rounded-xl overflow-hidden mb-6 -mt-12">
              <img
                src="https://framerusercontent.com/images/0XK2wgaHskIE0XvlI8000WaXusA.png"
                alt="Messages interface"
                className="w-full h-64 object-cover"
              />
            </div>

            {/* Bottom Image - Simple positioning first */}
            <div className="bg-gray-800 rounded-xl overflow-hidden mt-8 h-96">
              <img
                src="https://framerusercontent.com/images/skP11X6IqJufQvxfS58erYQMbns.png?scale-down-to=2048"
                alt="Profile interface"
                className="w-full h-full object-cover"
              />
            </div>
          </div>

          {/* Part 3: Large Tutor Profile - Slightly smaller */}
          <div className="w-5/12 relative overflow-hidden">
            <img
              src="https://framerusercontent.com/images/skP11X6IqJufQvxfS58erYQMbns.png?scale-down-to=2048"
              alt="Tutor profile interface"
              className="w-full h-full object-cover object-left"
              style={{width: '220%', minHeight: '650px'}}
            />
          </div>
        </div>
      </section>
    </div>
  )
}
