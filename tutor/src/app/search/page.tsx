'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import Avatar from '@/components/avatar'
import TutorCardWithStatus from '@/components/tutor-card-with-status'
import AppHeader from '@/components/app-header'
import Footer from '@/components/footer'
import { ChevronDown, ChevronUp, Filter, X } from 'lucide-react'
import VerifiedIcon from '@/components/ui/verified-icon'

interface Tutor {
  id: string
  username: string | null
  full_name: string
  bio: string | null
  subjects: string[] | null
  languages: string[] | null
  hourly_rate: number | null
  tutor_tier: string | null
  tutor_status: string | null
  created_at: string
  average_rating: number | null
  total_sessions_taught: number | null
  profile_picture_url: string | null
  experience_years: number | null
}

export default function SearchPage() {
  const router = useRouter()
  const [tutors, setTutors] = useState<Tutor[]>([])
  const [filteredTutors, setFilteredTutors] = useState<Tutor[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [tierFilter, setTierFilter] = useState('')
  const [subjectFilter, setSubjectFilter] = useState<string[]>([])
  const [levelFilter, setLevelFilter] = useState<string[]>([])
  const [experienceFilter, setExperienceFilter] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<User | null>(null)

  // Predefined options
  const [subjects, setSubjects] = useState<string[]>([])
  const [levels, setLevels] = useState<string[]>([])

  // Accordion states
  const [tierAccordionOpen, setTierAccordionOpen] = useState(true)
  const [subjectAccordionOpen, setSubjectAccordionOpen] = useState(false)
  const [levelAccordionOpen, setLevelAccordionOpen] = useState(false)
  const [experienceAccordionOpen, setExperienceAccordionOpen] = useState(false)

  // Mobile filter state
  const [showMobileFilters, setShowMobileFilters] = useState(false)

  const supabase = createClient()

  const [revealed, setRevealed] = useState(false)

  // Staggered entrance on initial load, AFTER tutors are loaded
  useEffect(() => {
    if (!loading && filteredTutors.length > 0) {
      setRevealed(false)
      const t = setTimeout(() => setRevealed(true), 0)
      return () => clearTimeout(t)
    }
  }, [loading, filteredTutors.length])

  useEffect(() => {
    fetchTutors()
    fetchFilterOptions()
    checkUser()
  }, [])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  useEffect(() => {
    filterTutors()
  }, [tutors, searchTerm, tierFilter, subjectFilter, levelFilter, experienceFilter])

  const fetchTutors = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .order('created_at', { ascending: false })

      if (error) throw error
      setTutors(data || [])
    } catch (error) {
      console.error('Error fetching tutors:', error)
      setError('Failed to load tutors')
    } finally {
      setLoading(false)
    }
  }

  const fetchFilterOptions = async () => {
    try {
      const [subjectsRes, levelsRes] = await Promise.all([
        supabase.from('predefined_subjects').select('name').eq('is_active', true).order('display_order'),
        supabase.from('predefined_levels').select('name').eq('is_active', true).order('display_order')
      ])

      if (subjectsRes.data) setSubjects(subjectsRes.data.map(s => s.name))
      if (levelsRes.data) setLevels(levelsRes.data.map(l => l.name))
    } catch (error) {
      console.error('Error fetching filter options:', error)
    }
  }

  const filterTutors = () => {
    let filtered = tutors

    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase()
      filtered = filtered.filter(tutor => {
        if (tutor.full_name?.toLowerCase().includes(searchLower)) return true
        if (tutor.bio?.toLowerCase().includes(searchLower)) return true
        if (tutor.subjects?.some(subject =>
          subject.toLowerCase().includes(searchLower)
        )) return true
        return false
      })
    }

    if (tierFilter) {
      filtered = filtered.filter(tutor => tutor.tutor_tier === tierFilter)
    }

    if (subjectFilter.length > 0) {
      filtered = filtered.filter(tutor =>
        tutor.subjects?.some(subject =>
          subjectFilter.some(filter =>
            subject.toLowerCase().includes(filter.toLowerCase())
          )
        )
      )
    }

    if (levelFilter.length > 0) {
      filtered = filtered.filter(tutor =>
        tutor.subjects?.some(subject =>
          levelFilter.some(filter =>
            subject.toLowerCase().includes(filter.toLowerCase())
          )
        ) || levelFilter.some(filter =>
          tutor.bio?.toLowerCase().includes(filter.toLowerCase())
        )
      )
    }

    if (experienceFilter) {
      const minExperience = parseInt(experienceFilter)
      filtered = filtered.filter(tutor => {
        // Check if tutor has experience_years >= minExperience
        // Or if they have subject_experience data that matches
        if (tutor.experience_years && tutor.experience_years >= minExperience) {
          return true
        }
        // TODO: Add subject_experience check when database is updated
        return false
      })
    }

    setFilteredTutors(filtered)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tutors...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-gray-900 text-white px-4 py-2 rounded hover:bg-gray-800"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="page-container">
      <AppHeader />

      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Mobile Filter Button */}
        <div className="lg:hidden mb-4">
          <button
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="flex items-center justify-center w-full bg-white border border-gray-200 rounded-lg px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <Filter className="w-4 h-4 mr-2" />
            Filters
            {(tierFilter || subjectFilter.length > 0 || levelFilter.length > 0 || experienceFilter) && (
              <span className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                Active
              </span>
            )}
          </button>
        </div>

        <div className="flex flex-col lg:flex-row gap-8">
          {/* Mobile Filter Overlay */}
          {showMobileFilters && (
            <div className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setShowMobileFilters(false)}>
              <div className="bg-white h-full w-full max-w-sm ml-auto p-6 overflow-y-auto" onClick={(e) => e.stopPropagation()}>
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-900">Filters</h2>
                  <button
                    onClick={() => setShowMobileFilters(false)}
                    className="p-2 hover:bg-gray-100 rounded-full"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* Mobile Filter Content - Same as desktop but in overlay */}
                <div className="space-y-4">
                  <div>
                    <input
                      type="text"
                      placeholder="Search by name, subject, or expertise..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  {/* Mobile Tier Filter */}
                  <div className="border border-gray-200 rounded-lg">
                    <button
                      onClick={() => setTierAccordionOpen(!tierAccordionOpen)}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                    >
                      <h3 className="text-sm font-semibold text-gray-900">Tutor Tier</h3>
                      {tierAccordionOpen ? (
                        <ChevronUp className="w-4 h-4 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                      )}
                    </button>
                    {tierAccordionOpen && (
                      <div className="px-4 pb-4 space-y-2">
                        {['standard', 'verified'].map(tier => (
                          <label key={tier} className="flex items-center">
                            <input
                              type="radio"
                              name="tier"
                              value={tier}
                              checked={tierFilter === tier}
                              onChange={(e) => setTierFilter(e.target.value)}
                              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700 capitalize">{tier}</span>
                          </label>
                        ))}
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="tier"
                            value=""
                            checked={tierFilter === ''}
                            onChange={(e) => setTierFilter(e.target.value)}
                            className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">All Tiers</span>
                        </label>
                      </div>
                    )}
                  </div>

                  {/* Mobile Subject Filter */}
                  <div className="border border-gray-200 rounded-lg">
                    <button
                      onClick={() => setSubjectAccordionOpen(!subjectAccordionOpen)}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                    >
                      <h3 className="text-sm font-semibold text-gray-900">Subject</h3>
                      {subjectAccordionOpen ? (
                        <ChevronUp className="w-4 h-4 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                      )}
                    </button>
                    {subjectAccordionOpen && (
                      <div className="px-4 pb-4 space-y-2">
                        {subjects.map(subject => (
                          <label key={subject} className="flex items-center">
                            <input
                              type="checkbox"
                              value={subject}
                              checked={subjectFilter.includes(subject)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setSubjectFilter([...subjectFilter, subject])
                                } else {
                                  setSubjectFilter(subjectFilter.filter(s => s !== subject))
                                }
                              }}
                              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">{subject}</span>
                          </label>
                        ))}
                        {subjectFilter.length > 0 && (
                          <button
                            onClick={() => setSubjectFilter([])}
                            className="text-sm text-blue-600 hover:text-blue-800 mt-2"
                          >
                            Clear All
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Mobile Level Filter */}
                  <div className="border border-gray-200 rounded-lg">
                    <button
                      onClick={() => setLevelAccordionOpen(!levelAccordionOpen)}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                    >
                      <h3 className="text-sm font-semibold text-gray-900">Level</h3>
                      {levelAccordionOpen ? (
                        <ChevronUp className="w-4 h-4 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                      )}
                    </button>
                    {levelAccordionOpen && (
                      <div className="px-4 pb-4 space-y-2">
                        {levels.map(level => (
                          <label key={level} className="flex items-center">
                            <input
                              type="checkbox"
                              value={level}
                              checked={levelFilter.includes(level)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setLevelFilter([...levelFilter, level])
                                } else {
                                  setLevelFilter(levelFilter.filter(l => l !== level))
                                }
                              }}
                              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">{level}</span>
                          </label>
                        ))}
                        {levelFilter.length > 0 && (
                          <button
                            onClick={() => setLevelFilter([])}
                            className="text-sm text-blue-600 hover:text-blue-800 mt-2"
                          >
                            Clear All
                          </button>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Mobile Experience Filter */}
                  <div className="border border-gray-200 rounded-lg">
                    <button
                      onClick={() => setExperienceAccordionOpen(!experienceAccordionOpen)}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
                    >
                      <h3 className="text-sm font-semibold text-gray-900">Experience</h3>
                      {experienceAccordionOpen ? (
                        <ChevronUp className="w-4 h-4 text-gray-500" />
                      ) : (
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                      )}
                    </button>
                    {experienceAccordionOpen && (
                      <div className="px-4 pb-4 space-y-2">
                        {['1', '2', '3', '5', '10'].map(years => (
                          <label key={years} className="flex items-center">
                            <input
                              type="radio"
                              name="experience"
                              value={years}
                              checked={experienceFilter === years}
                              onChange={(e) => setExperienceFilter(e.target.value)}
                              className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                            />
                            <span className="ml-2 text-sm text-gray-700">{years}+ years</span>
                          </label>
                        ))}
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="experience"
                            value=""
                            checked={experienceFilter === ''}
                            onChange={(e) => setExperienceFilter(e.target.value)}
                            className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                          />
                          <span className="ml-2 text-sm text-gray-700">Any Experience</span>
                        </label>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Desktop Sidebar */}
          <div className="hidden lg:block lg:w-80 bg-white rounded-lg shadow-sm border border-gray-200 p-6 h-fit">
            <div className="mb-6">
              <h2 className="text-xl font-bold text-gray-900 mb-2">Find Your Perfect Tutor</h2>
              <p className="text-gray-600 text-sm">Use filters to narrow down your search</p>
            </div>

            <div className="mb-6">
              <input
                type="text"
                placeholder="Search by name, subject, or expertise..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Desktop Tutor Tier Accordion */}
            <div className="mb-4 border border-gray-200 rounded-lg">
              <button
                onClick={() => setTierAccordionOpen(!tierAccordionOpen)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <h3 className="text-sm font-semibold text-gray-900">Tutor Tier</h3>
                {tierAccordionOpen ? (
                  <ChevronUp className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </button>
              {tierAccordionOpen && (
                <div className="px-4 pb-4 space-y-2">
                  {['standard', 'verified'].map(tier => (
                    <label key={tier} className="flex items-center">
                      <input
                        type="radio"
                        name="tier"
                        value={tier}
                        checked={tierFilter === tier}
                        onChange={(e) => setTierFilter(e.target.value)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700 capitalize">{tier}</span>
                    </label>
                  ))}
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="tier"
                      value=""
                      checked={tierFilter === ''}
                      onChange={(e) => setTierFilter(e.target.value)}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">All Tiers</span>
                  </label>
                </div>
              )}
            </div>

            {/* Subject Accordion */}
            <div className="mb-4 border border-gray-200 rounded-lg">
              <button
                onClick={() => setSubjectAccordionOpen(!subjectAccordionOpen)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <h3 className="text-sm font-semibold text-gray-900">Subject</h3>
                {subjectAccordionOpen ? (
                  <ChevronUp className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </button>
              {subjectAccordionOpen && (
                <div className="px-4 pb-4 space-y-2">
                  {subjects.map(subject => (
                    <label key={subject} className="flex items-center">
                      <input
                        type="checkbox"
                        value={subject}
                        checked={subjectFilter.includes(subject)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSubjectFilter([...subjectFilter, subject])
                          } else {
                            setSubjectFilter(subjectFilter.filter(s => s !== subject))
                          }
                        }}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{subject}</span>
                    </label>
                  ))}
                  {subjectFilter.length > 0 && (
                    <button
                      onClick={() => setSubjectFilter([])}
                      className="text-sm text-blue-600 hover:text-blue-800 mt-2"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Level Accordion */}
            <div className="mb-4 border border-gray-200 rounded-lg">
              <button
                onClick={() => setLevelAccordionOpen(!levelAccordionOpen)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <h3 className="text-sm font-semibold text-gray-900">Level</h3>
                {levelAccordionOpen ? (
                  <ChevronUp className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </button>
              {levelAccordionOpen && (
                <div className="px-4 pb-4 space-y-2">
                  {levels.map(level => (
                    <label key={level} className="flex items-center">
                      <input
                        type="checkbox"
                        value={level}
                        checked={levelFilter.includes(level)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setLevelFilter([...levelFilter, level])
                          } else {
                            setLevelFilter(levelFilter.filter(l => l !== level))
                          }
                        }}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{level}</span>
                    </label>
                  ))}
                  {levelFilter.length > 0 && (
                    <button
                      onClick={() => setLevelFilter([])}
                      className="text-sm text-blue-600 hover:text-blue-800 mt-2"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              )}
            </div>

            {/* Experience Accordion */}
            <div className="mb-4 border border-gray-200 rounded-lg">
              <button
                onClick={() => setExperienceAccordionOpen(!experienceAccordionOpen)}
                className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50"
              >
                <h3 className="text-sm font-semibold text-gray-900">Experience</h3>
                {experienceAccordionOpen ? (
                  <ChevronUp className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                )}
              </button>
              {experienceAccordionOpen && (
                <div className="px-4 pb-4 space-y-2">
                  {['1', '2', '3', '5', '10'].map(years => (
                    <label key={years} className="flex items-center">
                      <input
                        type="radio"
                        name="experience"
                        value={years}
                        checked={experienceFilter === years}
                        onChange={(e) => setExperienceFilter(e.target.value)}
                        className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{years}+ years</span>
                    </label>
                  ))}
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="experience"
                      value=""
                      checked={experienceFilter === ''}
                      onChange={(e) => setExperienceFilter(e.target.value)}
                      className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Any Experience</span>
                  </label>
                </div>
              )}
            </div>
          </div>

          {/* Main Content Area */}
          <div className="flex-1 w-full lg:w-auto">
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {filteredTutors.length} Tutors Available
              </h1>
              <p className="text-gray-600">
                {searchTerm && `Results for "${searchTerm}"`}
              </p>
            </div>

            {filteredTutors.length === 0 ? (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold mb-2">No tutors found</h3>
                <p className="text-gray-500">Try adjusting your search criteria or filters</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredTutors.map((tutor, idx) => (
                  <TutorCardWithStatus
                    key={tutor.id}
                    tutor={tutor}
                    onClick={() => router.push(`/${tutor.id}`)}
                  />


                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  )
}