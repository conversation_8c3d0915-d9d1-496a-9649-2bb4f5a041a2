'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import type { User } from '@supabase/supabase-js'
import Logo from '@/components/logo'

type UserRole = 'student' | 'tutor' | 'admin'
type ApprovalStatus = 'pending' | 'approved' | 'rejected'

interface RoleStatus {
  role: UserRole
  status: ApprovalStatus | null
  can_access: boolean
}

interface Profile {
  id: string
  email: string
  full_name: string
  roles: UserRole[]
  student_status: ApprovalStatus
  tutor_status: ApprovalStatus | null
  admin_status: ApprovalStatus | null
}

export default function RoleManagementPage() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<Profile | null>(null)
  const [roleStatuses, setRoleStatuses] = useState<RoleStatus[]>([])
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState<UserRole | null>(null)
  const [message, setMessage] = useState('')

  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    loadUserData()
  }, [])

  const loadUserData = async () => {
    try {
      const { data: { user }, error: userError } = await supabase.auth.getUser()
      
      if (userError || !user) {
        router.push('/auth')
        return
      }

      setUser(user)

      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        console.error('Profile error:', profileError)
        setMessage('Error loading profile. Please try refreshing the page.')
        
        // Try to create a basic profile if it doesn't exist
        if (profileError.code === 'PGRST116') {
          try {
            const { error: createError } = await supabase
              .from('profiles')
              .insert({
                id: user.id,
                email: user.email,
                full_name: user.user_metadata?.full_name || 'User',
                roles: ['student'],
                student_status: 'approved'
              })
            
            if (!createError) {
              setMessage('Profile created successfully! Please refresh the page.')
            }
          } catch (createError) {
            console.error('Error creating profile:', createError)
          }
        }
        return
      }

      setProfile(profile)

      // Get role statuses
      const { data: statuses, error: statusError } = await supabase
        .rpc('get_user_role_status', { user_id: user.id })

      if (statusError) {
        console.error('Role status error:', statusError)
      } else {
        setRoleStatuses(statuses || [])
      }

    } catch (error) {
      console.error('Error loading user data:', error)
      setMessage('Error loading user data')
    } finally {
      setLoading(false)
    }
  }

  const handleRoleRequest = async (role: UserRole) => {
    if (!user || processing) return

    setProcessing(role)
    setMessage('')

    try {
      // For tutor role, redirect to account page instead of creating role request
      if (role === 'tutor') {
        setMessage('Redirecting to complete your tutor application...')
        setTimeout(() => {
          router.push('/profile?tab=tutor-apply')
        }, 1500)
        return
      }

      // For other roles, proceed with normal role request
      const { error } = await supabase
        .rpc('add_user_role', {
          user_id: user.id,
          new_role: role
        })

      if (error) {
        throw error
      }

      if (role === 'student') {
        setMessage('Student role added successfully! You can now browse and book tutors.')
      } else {
        setMessage(`${role} role added successfully!`)
      }
      await loadUserData() // Refresh data

    } catch (error) {
      setMessage(error instanceof Error ? error.message : `Failed to request ${role} role`)
    } finally {
      setProcessing(null)
    }
  }

  const getRoleInfo = (role: UserRole) => {
    const status = roleStatuses.find(r => r.role === role)
    const hasRole = profile?.roles?.includes(role)

    return {
      hasRole,
      status: status?.status,
      canAccess: status?.can_access || false,
      ...getRoleConfig(role)
    }
  }

  const getRoleConfig = (role: UserRole) => {
    switch (role) {
      case 'student':
        return {
          title: 'Student',
          description: 'Browse and book sessions with verified tutors',
          icon: '📚',
          color: 'blue',
          features: ['Browse tutors', 'Book sessions', 'Track progress', 'Leave reviews']
        }
      case 'tutor':
        return {
          title: 'Tutor', 
          description: 'Teach students and share your expertise',
          icon: '👨‍🏫',
          color: 'green',
          features: ['Create profile', 'Set availability', 'Manage students', 'Earn money']
        }
      case 'admin':
        return {
          title: 'Administrator',
          description: 'Manage platform and approve tutors',
          icon: '⚙️', 
          color: 'red',
          features: ['Approve tutors', 'Platform analytics', 'User management', 'System oversight']
        }
      default:
        return { title: '', description: '', icon: '', color: 'gray', features: [] }
    }
  }

  const getStatusBadge = (role: UserRole) => {
    const info = getRoleInfo(role)
    
    if (!info.hasRole) {
      return (
        <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
          Not Active
        </span>
      )
    }

    const statusColors = {
      approved: 'bg-green-100 text-green-800',
      pending: 'bg-yellow-100 text-yellow-800', 
      rejected: 'bg-red-100 text-red-800'
    }

    const color = info.status ? statusColors[info.status] : 'bg-gray-100 text-gray-600'

    return (
      <span className={`px-2 py-1 text-xs rounded-full ${color}`}>
        {info.status ? info.status.charAt(0).toUpperCase() + info.status.slice(1) : 'Unknown'}
      </span>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading roles...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="page-container">
      {/* Header */}
      <header className="header-standard">
        <div className="w-full h-full flex justify-between items-center">
          {/* Logo - Very Left */}
          <div className="pl-4">
            <Logo href="/" size="md" />
          </div>

          {/* Right Side Navigation - All items together */}
          <div className="nav-right">
            {/* Navigation Links */}
            <nav className="nav-links">
              <Link href="/search" className="nav-link">Find Tutors</Link>
              <Link href="/tutoring" className="nav-link">Become a Tutor</Link>
              <Link href="/dashboard" className="nav-link">Dashboard</Link>
            </nav>

            {/* Auth UI */}
            <div className="auth-ui">
              <span className="text-sm text-gray-600 hidden lg:block">
                {profile?.full_name || user?.email}
              </span>
              <Link href="/dashboard">
                <Button variant="outline" size="sm">Back to Dashboard</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Current Roles Overview */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-4">Your Current Roles</h2>
          <div className="bg-card rounded-lg p-4 border">
            <div className="flex flex-wrap gap-2">
              {profile?.roles?.map(role => {
                const info = getRoleInfo(role)
                return (
                  <div key={role} className="flex items-center gap-2">
                    <span className="text-lg">{info.icon}</span>
                    <span className="font-medium">{info.title}</span>
                    {getStatusBadge(role)}
                  </div>
                )
              }) || <span className="text-muted-foreground">No active roles</span>}
            </div>
          </div>
        </div>

        {/* Available Roles */}
        <div className="space-y-6">
          <h2 className="text-xl font-semibold">Available Roles</h2>
          
          {(['student', 'tutor', 'admin'] as UserRole[]).map(role => {
            const info = getRoleInfo(role)
            const isDisabled = role === 'admin' // Admin role requires special assignment
            
            return (
              <div key={role} className="bg-card rounded-lg p-6 border">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-3">
                      <span className="text-2xl">{info.icon}</span>
                      <div>
                        <h3 className="text-lg font-semibold">{info.title}</h3>
                        <p className="text-muted-foreground">{info.description}</p>
                      </div>
                      {getStatusBadge(role)}
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4 mb-4">
                      <div>
                        <h4 className="font-medium mb-2">Features</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {info.features.map((feature, index) => (
                            <li key={index} className="flex items-center gap-2">
                              <span className="w-1.5 h-1.5 bg-primary rounded-full"></span>
                              {feature}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      {role === 'tutor' && info.hasRole && info.status !== 'approved' && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
                          <h4 className="font-medium text-yellow-800 mb-1">Approval Required</h4>
                          <p className="text-sm text-yellow-700">
                            Your tutor application is under review. You&apos;ll be notified once approved.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="ml-4">
                    {!info.hasRole ? (
                      <Button
                        onClick={() => handleRoleRequest(role)}
                        disabled={processing === role || isDisabled}
                        className={`${info.color === 'green' ? 'bg-green-600 hover:bg-green-700' : 
                                   info.color === 'red' ? 'bg-red-600 hover:bg-red-700' : ''}`}
                      >
                        {processing === role ? 'Requesting...' : 
                         isDisabled ? 'Contact Admin' :
                         role === 'tutor' ? 'Apply to Tutor' : `Become ${info.title}`}
                      </Button>
                    ) : (
                      <Button disabled variant="outline">
                        {info.canAccess ? 'Active' : 'Pending Approval'}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {message && (
          <div className={`mt-6 p-4 rounded-lg ${
            message.includes('successfully') || message.includes('added') 
              ? 'bg-green-50 border border-green-200 text-green-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            {message}
          </div>
        )}

        {/* Info Section */}
        <div className="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-800 mb-2">💡 How Roles Work</h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• <strong>Student role</strong> is automatically approved and gives you access to find and book tutors</li>
            <li>• <strong>Tutor role</strong> requires admin approval before you can start teaching students</li>
            <li>• <strong>Admin role</strong> must be assigned by existing administrators</li>
            <li>• You can have multiple roles simultaneously - they complement each other</li>
          </ul>
        </div>
      </div>
    </div>
  )
}