import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import "../styles/professional-images.css";
import PresenceProvider from "@/components/presence-provider";
import DeploymentIndicator from "@/components/deployment-indicator";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ProTutor - Find Expert Tutors Online",
  description: "Connect with verified tutors for personalized learning. Set your own rate, teach on your schedule, or find the perfect tutor for your needs.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <PresenceProvider>
          {children}
        </PresenceProvider>
        <DeploymentIndicator />
      </body>
    </html>
  );
}
