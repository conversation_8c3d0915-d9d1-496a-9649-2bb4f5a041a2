'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Calendar, Clock, DollarSign, User, MessageCircle, CheckCircle, XCircle } from 'lucide-react'
import AppHeader from '@/components/app-header'
import Footer from '@/components/footer'
import Avatar from '@/components/avatar'

interface BookingRequest {
  id: string
  student_id: string
  tutor_id: string
  requested_date: string
  requested_time: string
  subject: string
  session_length: number
  message: string | null
  status: 'pending' | 'confirmed' | 'accepted' | 'declined' | 'completed' | 'cancelled'
  total_amount: number
  decline_reason: string | null
  created_at: string
  tutor_profile?: {
    id: string
    full_name: string
    email: string
    avatar_url: string | null
    profile_picture_url: string | null
  }
}

export default function MyBookingsPage() {
  const [user, setUser] = useState<any>(null)
  const [bookingRequests, setBookingRequests] = useState<BookingRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'pending' | 'accepted' | 'declined' | 'completed' | 'all'>('pending')
  const supabase = createClient()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      fetchBookingRequests()
    }
  }, [user])

  const checkUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    } catch (error) {
      console.error('Error checking user:', error)
    }
  }

  const fetchBookingRequests = async () => {
    try {
      setLoading(true)
      
      // Get booking requests for this student
      const { data: bookingData, error: bookingError } = await supabase
        .from('booking_requests')
        .select('*')
        .eq('student_id', user.id)
        .order('created_at', { ascending: false })

      if (bookingError) {
        console.error('Booking requests error:', bookingError)
        throw bookingError
      }

      // Get tutor profiles for each booking
      const bookingRequestsWithProfiles = await Promise.all(
        (bookingData || []).map(async (booking) => {
          const { data: tutorProfile } = await supabase
            .from('profiles')
            .select('id, full_name, email, avatar_url, profile_picture_url')
            .eq('id', booking.tutor_id)
            .single()

          return {
            ...booking,
            tutor_profile: tutorProfile
          }
        })
      )

      setBookingRequests(bookingRequestsWithProfiles)
    } catch (error) {
      console.error('Error fetching booking requests:', error)
      setError(`Failed to load booking requests: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatAmount = (amountInCents: number) => {
    return `$${(amountInCents / 100).toFixed(2)}`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'accepted':
        return 'bg-green-100 text-green-800'
      case 'declined':
        return 'bg-red-100 text-red-800'
      case 'completed':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'accepted':
        return <CheckCircle className="w-4 h-4" />
      case 'declined':
        return <XCircle className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const filteredBookings = activeTab === 'all' 
    ? bookingRequests 
    : bookingRequests.filter(booking => booking.status === activeTab)

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading your bookings...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <h1 className="text-2xl font-bold">Please sign in</h1>
          <button 
            onClick={() => window.location.href = '/auth'}
            className="bg-gray-900 text-white px-4 py-2 rounded hover:bg-gray-800"
          >
            Go to Sign In
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AppHeader />
      
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Booking Requests</h1>
          <p className="text-gray-600">Track your tutoring session requests and their status</p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-lg w-fit">
          {[
            { key: 'pending', label: 'Pending', count: bookingRequests.filter(b => b.status === 'pending').length },
            { key: 'accepted', label: 'Accepted', count: bookingRequests.filter(b => b.status === 'accepted').length },
            { key: 'declined', label: 'Declined', count: bookingRequests.filter(b => b.status === 'declined').length },
            { key: 'completed', label: 'Completed', count: bookingRequests.filter(b => b.status === 'completed').length },
            { key: 'all', label: 'All', count: bookingRequests.length }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No booking requests</h3>
            <p className="text-gray-600 mb-4">
              {activeTab === 'pending' 
                ? "You don't have any pending booking requests at the moment."
                : `No ${activeTab} booking requests found.`
              }
            </p>
            <Button onClick={() => window.location.href = '/search'}>
              Find Tutors
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredBookings.map((booking) => (
              <Card key={booking.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-4">
                      <div className="professional-image-frame large w-28 h-28 rounded-xl overflow-hidden flex-shrink-0">
                        <div className="image-enhancement-wrapper w-full h-full relative">
                          <Avatar
                            src={booking.tutor_profile?.profile_picture_url || booking.tutor_profile?.avatar_url}
                            name={booking.tutor_profile?.full_name || 'Tutor'}
                            id={booking.tutor_profile?.id || booking.id}
                            size="xl"
                            className="w-full h-full"
                          />
                        </div>
                      </div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-gray-900">
                          Session with {booking.tutor_profile?.full_name || 'Tutor'}
                        </CardTitle>
                        <CardDescription className="text-base text-gray-600 mt-1">
                          {booking.tutor_profile?.email}
                        </CardDescription>
                        <div className="mt-2 text-sm text-gray-500">
                          Requested on {new Date(booking.created_at).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(booking.status)}>
                      <div className="flex items-center space-x-1">
                        {getStatusIcon(booking.status)}
                        <span>{booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}</span>
                      </div>
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span>{formatDate(booking.requested_date)}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span>{booking.requested_time} ({booking.session_length} minutes)</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <DollarSign className="w-4 h-4 text-gray-500" />
                        <span>{formatAmount(booking.total_amount)}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm"><strong>Subject:</strong> {booking.subject}</p>
                      {booking.message && (
                        <p className="text-sm"><strong>Your message:</strong> {booking.message}</p>
                      )}
                      {booking.decline_reason && (
                        <div className="bg-red-50 border border-red-200 rounded p-3">
                          <p className="text-sm text-red-800"><strong>Decline reason:</strong> {booking.decline_reason}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Status-specific actions */}
                  {booking.status === 'accepted' && (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                      <div className="flex items-center space-x-2 text-green-800 mb-2">
                        <CheckCircle className="w-5 h-5" />
                        <span className="font-medium">Session Confirmed!</span>
                      </div>
                      <p className="text-sm text-green-700 mb-3">
                        Your tutor has accepted your booking request. You'll receive session details and payment instructions soon.
                      </p>
                      <div className="flex space-x-3">
                        <Button size="sm" variant="outline">
                          <MessageCircle className="w-4 h-4 mr-2" />
                          Message Tutor
                        </Button>
                        <Button size="sm" variant="outline">
                          Add to Calendar
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Footer />
    </div>
  )
}
