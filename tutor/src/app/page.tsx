'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import { Search, LogIn, UserPlus } from 'lucide-react'
import AppHeader from '@/components/app-header'
import DiscoverTutorsGrid from '@/components/discover-tutors-grid'
import Footer from '@/components/footer'
// Simple scroll-reveal hook
function useScrollReveal() {
  const [ready, setReady] = useState(false)
  useEffect(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        const el = entry.target as HTMLElement
        if (entry.isIntersecting) {
          el.classList.add('opacity-100', 'translate-y-0')
          el.classList.remove('opacity-0', 'translate-y-4')
        }
      })
    }, { threshold: 0.12, rootMargin: '0px 0px -60px 0px' })

    document.querySelectorAll('[data-reveal]').forEach((el) => {
      el.classList.add('opacity-0', 'translate-y-4')
      ;(el as HTMLElement).style.transition = 'opacity 600ms ease, transform 600ms ease'
      observer.observe(el)
    })
    setReady(true)
    return () => observer.disconnect()
  }, [])
  return ready
}


// Matrix-style search suggestions
const subjectSuggestions = [
  'Mathematics', 'Physics', 'Chemistry', 'Biology', 'English', 'History',
  'Computer Science', 'Economics', 'Psychology', 'Philosophy', 'Languages', 'Art'
]

const levelSuggestions = [
  'Middle School', 'High School', 'IB Diploma', 'AP Courses',
  'College', 'University', 'Graduate', 'PhD Level'
]

export default function Home() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [subjectInput, setSubjectInput] = useState('')
  const [levelInput, setLevelInput] = useState('')
  const [showSubjectSuggestions, setShowSubjectSuggestions] = useState(false)
  const [showLevelSuggestions, setShowLevelSuggestions] = useState(false)
  const supabase = createClient()

  // Filter suggestions based on input
  const filteredSubjects = subjectSuggestions.filter(subject =>
    subject.toLowerCase().includes(subjectInput.toLowerCase())
  ).slice(0, 5)

  const filteredLevels = levelSuggestions.filter(level =>
    level.toLowerCase().includes(levelInput.toLowerCase())
  ).slice(0, 5)

  // Handle search function
  const handleSearch = () => {
    const params = new URLSearchParams()
    if (subjectInput) params.set('subject', subjectInput)
    if (levelInput) params.set('level', levelInput)
    const qs = params.toString()
    window.location.href = qs ? `/search?${qs}` : '/search'
  }

  // enable scroll reveal
  useScrollReveal()

  // Handle Enter key press
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch()
    }
  }

  useEffect(() => {
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [supabase.auth])

  return (
    <div className="page-container">
      {/* Hero Section with Full Viewport Height */}
      <div className="h-screen flex flex-col overflow-hidden relative">
        {/* Modern Navigation */}
        <div className="relative z-50">
          <AppHeader />
        </div>

        {/* Hero Section - Student Focused - Takes remaining viewport height */}
        <section className="bg-black text-white flex-1 flex items-center overflow-hidden relative">
          <div className="w-full px-4 md:px-8 lg:px-32">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
              {/* Part 1: Student-Focused Hero Copy */}
              <div className="lg:col-span-1 space-y-6 md:space-y-10 text-center lg:text-left">
                <div className="space-y-4 md:space-y-6">
                  <p className="text-gray-300 text-lg md:text-xl lg:text-2xl font-medium">Learn from the best.</p>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight tracking-tight">
                    Find your perfect tutor.
                  </h1>
                </div>

                <p className="text-base md:text-lg lg:text-xl xl:text-2xl text-white leading-relaxed max-w-lg mx-auto lg:mx-0">
                  Connect with expert tutors who understand your goals. Book instantly, learn effectively, succeed confidently.
                </p>

                <div className="pt-4 md:pt-6 space-y-4">
                  {/* Matrix-Inspired Search with Suggestions */}
                  <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 max-w-lg mx-auto lg:mx-0 relative">
                    {/* Subject Input - Primary (Left) */}
                    <div className="sm:flex-[1.2] relative">
                      <input
                        type="text"
                        placeholder="Subject"
                        value={subjectInput}
                        onChange={(e) => setSubjectInput(e.target.value)}
                        onFocus={() => setShowSubjectSuggestions(true)}
                        onBlur={() => setTimeout(() => setShowSubjectSuggestions(false), 200)}
                        onKeyPress={handleKeyPress}
                        className="w-full bg-white/5 border-0 rounded px-3 py-3 md:py-2 text-green-200 placeholder-white/80 focus:outline-none focus:bg-white/10 focus:text-green-100 focus:placeholder-green-300/80 text-sm md:text-sm font-mono tracking-wide transition-all duration-300 focus:shadow-sm focus:shadow-green-400/20"
                      />
                      {/* Matrix-style cursor inside input when focused or has content */}
                      {(subjectInput || showSubjectSuggestions) && (
                        <span className="absolute right-3 top-1/2 -translate-y-1/2 w-px h-4 bg-green-300 animate-pulse" aria-hidden="true"></span>
                      )}

                    {/* Matrix-style Subject Suggestions */}
                    {showSubjectSuggestions && filteredSubjects.length > 0 && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-black/90 backdrop-blur-sm border border-green-400/20 rounded overflow-hidden z-10">
                        {filteredSubjects.map((subject, index) => (
                          <button
                            key={subject}
                            onClick={() => {
                              setSubjectInput(subject)
                              setShowSubjectSuggestions(false)
                            }}
                            className="w-full text-left px-3 py-2 text-green-300 hover:bg-green-400/10 hover:text-green-200 text-sm font-mono tracking-wide transition-all duration-200 border-b border-green-400/10 last:border-b-0"
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                            <span className="opacity-60">&gt;</span> {subject}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                    {/* Level Input - Secondary (Right) */}
                    <div className="sm:flex-1 relative">
                      <input
                        type="text"
                        placeholder="Level"
                        value={levelInput}
                        onChange={(e) => setLevelInput(e.target.value)}
                        onFocus={() => setShowLevelSuggestions(true)}
                        onBlur={() => setTimeout(() => setShowLevelSuggestions(false), 200)}
                        onKeyPress={handleKeyPress}
                        className="w-full bg-white/5 border-0 rounded px-3 py-3 md:py-2 text-green-200 placeholder-white/80 focus:outline-none focus:bg-white/10 focus:text-green-100 focus:placeholder-green-300/80 text-sm md:text-sm font-mono tracking-wide transition-all duration-300 focus:shadow-sm focus:shadow-green-400/20"
                      />

                    {/* Matrix-style Level Suggestions */}
                    {showLevelSuggestions && filteredLevels.length > 0 && (
                      <div className="absolute top-full left-0 right-0 mt-1 bg-black/90 backdrop-blur-sm border border-green-400/20 rounded overflow-hidden z-10">
                        {filteredLevels.map((level, index) => (
                          <button
                            key={level}
                            onClick={() => {
                              setLevelInput(level)
                              setShowLevelSuggestions(false)
                            }}
                            className="w-full text-left px-3 py-2 text-green-300 hover:bg-green-400/10 hover:text-green-200 text-sm font-mono tracking-wide transition-all duration-200 border-b border-green-400/10 last:border-b-0"
                            style={{ animationDelay: `${index * 50}ms` }}
                          >
                            <span className="opacity-60">&gt;</span> {level}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>

                    <Link href={`/search?subject=${encodeURIComponent(subjectInput)}&level=${encodeURIComponent(levelInput)}`}>
                      <button className="w-full sm:w-auto bg-white/10 hover:bg-green-400/10 px-4 py-3 md:py-2 rounded transition-all duration-300 hover:shadow-sm hover:shadow-green-400/20 group">
                        <Search className="w-4 h-4 text-white group-hover:text-green-300 transition-colors duration-300" />
                      </button>
                    </Link>
                  </div>

                  <p className="text-gray-400 text-sm text-center lg:text-left">Join 10,000+ students worldwide • Updated</p>
                </div>
              </div>



              {/* Large Student Success Image - Hidden on mobile, visible on lg+ */}
              <div className="hidden lg:block lg:col-span-1 relative overflow-hidden z-10">
                <img
                  src="https://framerusercontent.com/images/skP11X6IqJufQvxfS58erYQMbns.png?scale-down-to=2048"
                  alt="Student success interface"
                  className="w-full h-full object-cover object-left"
                  style={{width: '120%', minHeight: '100%', maxHeight: '100%'}}
                />
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Discover Tutors Section */}
      <section className="bg-white py-20 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16" data-reveal>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">Discover Tutors</h2>
            <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
              Explore our community of expert tutors. Find the perfect match for your learning goals.
            </p>
          </div>

          <div data-reveal>
            <DiscoverTutorsGrid />
          </div>

          <div className="text-center mt-12" data-reveal>
            <Link href="/search">
              <Button className="bg-black text-white hover:bg-gray-800 text-lg px-8 py-4 flex items-center space-x-2 mx-auto">
                <Search className="w-5 h-5" />
                <span>View All Tutors</span>
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Popular Subjects Section */}
      <section className="bg-gray-50 py-20 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16" data-reveal>
            <h2 className="text-5xl font-bold text-gray-900 mb-6">Popular Subjects</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Find expert tutors in any subject. From core academics to test prep, we&apos;ve got you covered.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6" data-reveal>
            {/* Math */}
            <div className="bg-white rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">📊</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Mathematics</h3>
                <p className="text-gray-600">Algebra, Calculus, Statistics</p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">150+ tutors available</p>
                  <p className="text-lg font-semibold text-gray-900">From $25/hr</p>
                </div>
              </div>
            </div>

            {/* Science */}
            <div className="bg-white rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">🧪</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Science</h3>
                <p className="text-gray-600">Physics, Chemistry, Biology</p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">120+ tutors available</p>
                  <p className="text-lg font-semibold text-gray-900">From $30/hr</p>
                </div>
              </div>
            </div>

            {/* Languages */}
            <div className="bg-white rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">🌍</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Languages</h3>
                <p className="text-gray-600">English, Spanish, French</p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">80+ tutors available</p>
                  <p className="text-lg font-semibold text-gray-900">From $20/hr</p>
                </div>
              </div>
            </div>

            {/* Test Prep */}
            <div className="bg-white rounded-xl p-6 hover:shadow-lg transition-shadow cursor-pointer">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                  <span className="text-2xl">📝</span>
                </div>
                <h3 className="text-xl font-bold text-gray-900">Test Prep</h3>
                <p className="text-gray-600">SAT, ACT, IB, AP</p>
                <div className="space-y-2">
                  <p className="text-sm text-gray-500">90+ tutors available</p>
                  <p className="text-lg font-semibold text-gray-900">From $35/hr</p>
                </div>
              </div>
            </div>
          </div>

          <div className="text-center mt-12">
            <Link href="/search">
              <Button variant="outline" className="border-gray-900 text-gray-900 hover:bg-gray-900 hover:text-white text-lg px-8 py-4">
                View All Subjects
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  )
}