'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import type { User } from '@supabase/supabase-js'
import Avatar from '@/components/avatar'
import RectangularAvatar from '@/components/rectangular-avatar'
import AppHeader from '@/components/app-header'
import Footer from '@/components/footer'
import { Button } from '@/components/ui/button'
import {
  Star,
  Share2,
  Bookmark,
  Instagram,
  Linkedin,
  Twitter,
  Youtube,
  CheckCircle,
  ShieldCheck,
  ChevronLeft,
  ChevronRight,
  ChevronDown,
  PlusCircle,
  Plus,
  Mail,
  Bell,
  Clock
} from 'lucide-react'
import VerifiedIcon from '@/components/ui/verified-icon'
import FunctionalBookingSidebar from '@/components/functional-booking-sidebar'

interface TutorSpecialty {
  subject: string
  level: string
  topics: string[]
  description: string
}

interface Tutor {
  id: string
  username: string | null
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  languages: string[] | null
  profile_picture_url: string | null
  average_rating: number | null
  total_sessions_taught: number | null
  response_time_hours: number | null
  specializations: string[] | null
  created_at: string
  tutor_specialties?: TutorSpecialty[]
}

export default function TutorProfilePage() {
  const params = useParams()
  const router = useRouter()
  const tutorIdentifier = params.tutorId as string
  const [tutor, setTutor] = useState<Tutor | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [user, setUser] = useState<User | null>(null)
  const [showMoreBio, setShowMoreBio] = useState(false)

  const supabase = createClient()

  useEffect(() => {
    if (tutorIdentifier) {
      fetchTutor()
      checkUser()
    }
  }, [tutorIdentifier])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const fetchTutor = async () => {
    try {
      // Validate that tutorIdentifier is a UUID
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(tutorIdentifier)

      if (!isUUID) {
        setError('Invalid tutor profile URL')
        setLoading(false)
        return
      }

      const { data, error } = await supabase
        .from('profiles')
        .select(`
          *,
          tutor_specialties (
            subject,
            level,
            topics,
            description
          )
        `)
        .eq('id', tutorIdentifier)
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .single()

      if (error) throw error
      setTutor(data)
    } catch (error) {
      console.error('Error fetching tutor:', error)
      setError('Failed to load tutor profile')
    } finally {
      setLoading(false)
    }
  }

  const formatRate = (rate: number | null) => {
    if (!rate) return 'N/A'
    return `$${(rate / 100).toFixed(0)}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading tutor profile...</p>
        </div>
      </div>
    )
  }

  if (error || !tutor) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Tutor not found'}</p>
          <Button onClick={() => router.push('/search')}>
            Back to Search
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center">
      <AppHeader />

      {/* Main Content Area */}
      <div className="relative w-full max-w-7xl mx-auto mt-6 px-4 sm:px-6 lg:px-8">
        <div className="rounded-xl overflow-hidden shadow-lg bg-white">
        {/* Background Banner Image - Floral/Nature Image */}
        <div
          className="w-full h-64 bg-cover bg-center"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2071&q=80')`
          }}
        ></div>

        <div className="p-6 flex flex-col lg:flex-row -mt-32">
          {/* Left Section - Main Profile Card */}
          <div className="w-full lg:w-2/3 bg-white rounded-xl shadow-lg p-6 lg:mr-6 mb-6 lg:mb-0">
            {/* Header with breadcrumb and actions */}
            <div className="flex justify-between items-center mb-4">
              <div className="flex items-center text-sm text-gray-500">
                <span>Education</span>
                <ChevronRight className="w-4 h-4 mx-1 text-gray-400" />
                <span>{tutor.subjects?.[0] || 'Maths'}</span>
              </div>
              <div className="flex items-center space-x-4">
                <button className="flex items-center text-gray-600 hover:text-indigo-600 transition-colors">
                  <Share2 className="w-4 h-4 mr-1" />
                  <span className="text-sm font-medium">Share</span>
                </button>
                <button className="flex items-center text-gray-600 hover:text-indigo-600 transition-colors">
                  <Bookmark className="w-4 h-4 mr-1" />
                  <span className="text-sm font-medium">Save</span>
                </button>
              </div>
            </div>

            {/* Profile section with large image */}
            <div className="flex flex-col sm:flex-row items-start mb-6">
              <div className={`professional-image-frame large w-48 h-64 rounded-2xl overflow-hidden mb-4 sm:mb-0 sm:mr-6 flex-shrink-0 group transition-all duration-300 hover:shadow-2xl ${tutor.tutor_tier === 'verified' ? 'verified' : ''}`}>
                <div className="image-enhancement-wrapper w-full h-full relative">
                  <RectangularAvatar
                    src={tutor.profile_picture_url}
                    name={tutor.full_name}
                    id={tutor.id}
                    className="w-full h-full transition-all duration-300 group-hover:scale-[1.03] group-hover:brightness-110 group-hover:contrast-105"
                    popOnHover
                    showOnlineStatus={true}
                    isOnline={true} // Main profile always shows online
                  />
                  {/* Professional overlay effects */}
                  <div className="absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-black/20 pointer-events-none"></div>
                  <div className="absolute inset-0 ring-1 ring-white/20 ring-inset rounded-2xl pointer-events-none"></div>
                </div>
              </div>
              <div className="flex-grow">
                <div className="flex items-center mb-2">
                  <h1 className="text-3xl font-bold text-gray-900 mr-2">{tutor.full_name}</h1>
                  {tutor.tutor_tier === 'verified' && (
                    <VerifiedIcon size={24} className="ml-2" />
                  )}
                </div>
                <p className="text-lg text-gray-700 mb-3">
                  {tutor.subjects?.join(', ')} Tutor | High School and College
                </p>

                {/* Rating and social links */}
                <div className="flex items-center mb-4">
                  <Star className="w-4 h-4 fill-red-500 stroke-red-500 mr-1" />
                  <span className="text-red-500 font-semibold mr-2">
                    {tutor.average_rating?.toFixed(2) || '4.74'}
                  </span>
                  <span className="text-gray-500 mr-4">
                    ({tutor.total_sessions_taught || 238} reviews)
                  </span>
                  <div className="flex space-x-3">
                    <a href="#" className="text-gray-600 hover:text-gray-800">
                      <Instagram className="w-4 h-4" />
                    </a>
                    <a href="#" className="text-gray-600 hover:text-gray-800">
                      <Linkedin className="w-4 h-4" />
                    </a>
                    <a href="#" className="text-gray-600 hover:text-gray-800">
                      <Twitter className="w-4 h-4" />
                    </a>
                    <a href="#" className="text-gray-600 hover:text-gray-800">
                      <Youtube className="w-4 h-4" />
                    </a>
                  </div>
                </div>

                {/* Bio */}
                <p className="text-gray-700 leading-relaxed mb-2">
                  {showMoreBio ? tutor.bio : `${tutor.bio?.slice(0, 200)}...`}
                </p>
                <button
                  onClick={() => setShowMoreBio(!showMoreBio)}
                  className="text-indigo-600 hover:underline font-medium text-sm"
                >
                  {showMoreBio ? 'Show less' : 'Show more'}
                </button>
              </div>
            </div>

            {/* Specialties Section */}
            <div className="mb-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6">Specialties</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {tutor.tutor_specialties && tutor.tutor_specialties.length > 0 ? (
                  tutor.tutor_specialties.map((specialty, index) => (
                    <div key={index}>
                      <h3 className="font-semibold text-gray-800 mb-2">
                        {specialty.subject} - {specialty.level}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed mb-2">
                        {specialty.description}
                      </p>
                      {specialty.topics && specialty.topics.length > 0 && (
                        <p className="text-gray-500 text-xs">
                          <strong>Topics:</strong> {specialty.topics.join(', ')}
                        </p>
                      )}
                    </div>
                  ))
                ) : (
                  // Fallback to subjects if no specialties
                  tutor.subjects?.map((subject, index) => (
                    <div key={index}>
                      <h3 className="font-semibold text-gray-800 mb-2">{subject}</h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        Comprehensive tutoring in {subject.toLowerCase()} with personalized approach.
                      </p>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Meet your tutor section */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center mb-4">
                <div className={`professional-image-frame w-20 h-20 rounded-full overflow-hidden mr-3 flex-shrink-0 ${tutor.tutor_tier === 'verified' ? 'verified' : ''}`}>
                  <div className="image-enhancement-wrapper w-full h-full relative">
                    <Avatar
                      src={tutor.profile_picture_url}
                      name={tutor.full_name}
                      id={tutor.id}
                      size="lg"
                      className="w-full h-full rounded-full"
                    />
                  </div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-800">Meet your tutor, {tutor.full_name.split(' ')[0]}</h3>
                  <p className="text-sm text-gray-500">Host on Popless since {new Date(tutor.created_at).getFullYear()}</p>
                </div>
              </div>

              {/* Stats with red star */}
              <div className="flex flex-wrap items-center text-gray-600 text-sm mb-4">
                <div className="flex items-center mr-6 mb-2">
                  <Star className="w-4 h-4 fill-red-500 stroke-red-500 mr-1" />
                  <span className="text-red-500">{tutor.total_sessions_taught || 238} reviews</span>
                </div>
                <div className="flex items-center mr-6 mb-2">
                  <VerifiedIcon size={12} className="mr-2" />
                  <span>Identity verified</span>
                </div>
                <div className="flex items-center mb-2">
                  <Clock className="w-3 h-3 text-gray-600 mr-2" />
                  <span>{tutor.response_time_hours || 4} hr response time</span>
                </div>
              </div>

              <p className="text-gray-700 mb-6">
                Languages spoken: {tutor.languages?.join(', ') || 'English, Spanish, German, and French'}.
              </p>

              <div className="flex items-start space-x-4">
                <Link href={`/${tutor.id}/book`}>
                  <Button className="bg-gray-800 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-900 transition-colors">
                    Contact tutor
                  </Button>
                </Link>
                <div className="flex items-start text-gray-500 text-xs max-w-md">
                  <ShieldCheck className="w-4 h-4 mr-2 text-orange-500 flex-shrink-0 mt-0.5" />
                  <span>To protect your payment, never transfer money or communicate outside of the Popless website or app.</span>
                </div>
              </div>
            </div>
          </div>

          {/* Functional Booking Sidebar */}
          <FunctionalBookingSidebar
            tutorId={tutor.id}
            tutorName={tutor.full_name}
            hourlyRate={tutor.hourly_rate || 4500}
            subjects={tutor.subjects || ['General Tutoring']}
          />
        </div>
        </div>
      </div>

      {/* Footer */}
      <div className="w-full">
        <Footer />
      </div>
    </div>
  )
}
