'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { createClient } from '@/lib/supabase/client'
import { Calendar, Clock, DollarSign, ArrowLeft, User } from 'lucide-react'
import Logo from '@/components/logo'
import Footer from '@/components/footer'
import { WeeklyAvailabilityRange, getAvailableSlotsForDate, generateNextNDates, hasAnyAvailabilityForDate } from '@/lib/availability'

interface Tutor {
  id: string
  full_name: string
  bio: string | null
  tutor_tier: 'standard' | 'verified'
  hourly_rate: number | null
  subjects: string[] | null
  profile_picture_url: string | null
}

export default function BookTutorPage() {
  const params = useParams()
  const router = useRouter()
  const tutorId = params.tutorId as string
  const [tutor, setTutor] = useState<Tutor | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState<string>('')
  const [selectedTime, setSelectedTime] = useState<string>('')
  const [selectedSubject, setSelectedSubject] = useState<string>('')
  const [sessionLength, setSessionLength] = useState<number>(60)
  const [message, setMessage] = useState<string>('')
  const [bookingLoading, setBookingLoading] = useState(false)
  const [availableSubjects, setAvailableSubjects] = useState<string[]>([])
  const [weeklyAvailability, setWeeklyAvailability] = useState<WeeklyAvailabilityRange[]>([])
  const [availabilityLoading, setAvailabilityLoading] = useState(true)
  const supabase = createClient()

  useEffect(() => {
    if (tutorId) {
      fetchTutor()
      fetchAvailableSubjects()
      fetchTutorAvailability()
    }
  }, [tutorId])

  const fetchTutor = async () => {
    try {
      setLoading(true)
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', tutorId)
        .contains('roles', ['tutor'])
        .eq('tutor_status', 'approved')
        .single()

      if (error) throw error
      setTutor(data)
    } catch (error) {
      console.error('Error fetching tutor:', error)
      setError('Tutor not found or not available')
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableSubjects = async () => {
    try {
      const { data, error } = await supabase
        .from('predefined_subjects')
        .select('name')
        .eq('is_active', true)
        .order('display_order')

      if (error) throw error
      setAvailableSubjects(data?.map(s => s.name) || [])
    } catch (error) {
      console.error('Error fetching subjects:', error)
      setAvailableSubjects(['Mathematics', 'Sciences', 'Literature', 'Social Studies', 'Languages'])
    }
  }

  const fetchTutorAvailability = async () => {
    try {
      setAvailabilityLoading(true)
      const { data, error } = await supabase
        .from('tutor_availability')
        .select('day_of_week, start_time, end_time, is_available')
        .eq('tutor_id', tutorId)

      if (error) throw error
      setWeeklyAvailability(data || [])
    } catch (error) {
      console.error('Error fetching tutor availability:', error)
      setWeeklyAvailability([])
    } finally {
      setAvailabilityLoading(false)
    }
  }

  const formatRate = (rateInCents: number | null) => {
    if (!rateInCents) return 'Rate not set'
    return `$${(rateInCents / 100).toFixed(0)}`
  }

  const calculateTotal = () => {
    if (!tutor?.hourly_rate) return 0
    return (tutor.hourly_rate / 100) * (sessionLength / 60)
  }

  const handleBooking = async () => {
    if (!selectedDate || !selectedTime || !selectedSubject) {
      alert('Please fill in all required fields')
      return
    }

    // Validate that the selected time is still available
    const availableSlots = getAvailableTimeSlotsForDate(selectedDate)
    if (!availableSlots.includes(selectedTime)) {
      alert('Sorry, this time slot is no longer available. Please select a different time.')
      setSelectedTime('')
      return
    }

    try {
      setBookingLoading(true)
      
      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        router.push('/auth')
        return
      }

      // Create booking request
      const { data, error } = await supabase
        .from('booking_requests')
        .insert({
          student_id: user.id,
          tutor_id: tutorId,
          requested_date: selectedDate,
          requested_time: selectedTime,
          subject: selectedSubject,
          session_length: sessionLength,
          message: message,
          status: 'pending',
          total_amount: Math.round(calculateTotal() * 100) // Store in cents
        })

      if (error) throw error

      alert('Booking request sent successfully! The tutor will review and respond to your request.')
      router.push(`/${tutorId}`)
    } catch (error) {
      console.error('Error creating booking:', error)
      alert('Failed to create booking request. Please try again.')
    } finally {
      setBookingLoading(false)
    }
  }

  // Generate next 14 days
  const allDates = generateNextNDates(14)

  // Filter dates to only show those with availability
  const availableDates = allDates.filter(date =>
    hasAnyAvailabilityForDate(date, weeklyAvailability)
  )

  // Get available time slots for selected date
  const getAvailableTimeSlotsForDate = (date: string) => {
    if (!date) return []
    return getAvailableSlotsForDate(date, weeklyAvailability)
  }

  // Get time slots for currently selected date
  const availableTimeSlots = selectedDate ? getAvailableTimeSlotsForDate(selectedDate) : []

  if (loading) {
    return (
      <div className="page-container">
        <header className="header-standard">
          <div className="w-full h-full flex justify-between items-center">
            <div className="pl-4">
              <Logo href="/" size="md" />
            </div>
          </div>
        </header>
        
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </div>
    )
  }

  if (error || !tutor) {
    return (
      <div className="page-container">
        <header className="header-standard">
          <div className="w-full h-full flex justify-between items-center">
            <div className="pl-4">
              <Logo href="/" size="md" />
            </div>
          </div>
        </header>
        
        <div className="text-center py-20">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Tutor Not Found</h1>
          <p className="text-gray-600 mb-8">The tutor you're looking for doesn't exist or is not available.</p>
          <Link href="/search">
            <Button>Browse All Tutors</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="page-container">
      {/* Header */}
      <header className="header-standard">
        <div className="w-full h-full flex justify-between items-center">
          <div className="pl-4">
            <Logo href="/" size="md" />
          </div>
          <div className="nav-right">
            <nav className="nav-links">
              <Link href="/search" className="nav-link">Find Tutors</Link>
              <Link href="/tutoring" className="nav-link">Become a Tutor</Link>
              <Link href="/dashboard" className="nav-link">Dashboard</Link>
            </nav>
          </div>
        </div>
      </header>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Back Button */}
        <Link href={`/${tutorId}`} className="inline-flex items-center space-x-2 text-gray-600 hover:text-gray-900 mb-6">
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Profile</span>
        </Link>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Booking Form */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="w-5 h-5" />
                  <span>Book a Session</span>
                </CardTitle>
                <CardDescription>
                  {`Schedule a tutoring session with ${tutor.full_name}`}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Subject Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Subject *
                  </label>
                  <select
                    value={selectedSubject}
                    onChange={(e) => setSelectedSubject(e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a subject</option>
                    {availableSubjects.map(subject => (
                      <option key={subject} value={subject}>
                        {subject}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Date Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date * {availabilityLoading && <span className="text-xs text-gray-500">(Loading availability...)</span>}
                  </label>
                  {availableDates.length === 0 && !availabilityLoading ? (
                    <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                      <p className="text-sm text-yellow-800">
                        This tutor hasn't set their availability yet. Please contact them directly or try again later.
                      </p>
                    </div>
                  ) : (
                    <select
                      value={selectedDate}
                      onChange={(e) => {
                        setSelectedDate(e.target.value)
                        setSelectedTime('') // Reset time when date changes
                      }}
                      className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                      disabled={availabilityLoading}
                    >
                      <option value="">Select a date</option>
                      {availableDates.map(date => (
                        <option key={date} value={date}>
                          {new Date(date).toLocaleDateString('en-US', {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })} ✓
                        </option>
                      ))}
                    </select>
                  )}
                </div>

                {/* Time Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Time * {selectedDate && availabilityLoading && <span className="text-xs text-gray-500">(Loading availability...)</span>}
                  </label>
                  {!selectedDate ? (
                    <p className="text-sm text-gray-500 p-3 bg-gray-50 rounded-md">
                      Please select a date first to see available times
                    </p>
                  ) : availableTimeSlots.length === 0 ? (
                    <p className="text-sm text-gray-500 p-3 bg-gray-50 rounded-md">
                      No available times for this date. Please select a different date.
                    </p>
                  ) : (
                    <div className="grid grid-cols-3 gap-2">
                      {availableTimeSlots.map(time => (
                        <button
                          key={time}
                          onClick={() => setSelectedTime(time)}
                          className={`p-2 text-sm border rounded-md transition-colors ${
                            selectedTime === time
                              ? 'bg-green-600 text-white border-green-600'
                              : 'bg-green-50 text-green-700 border-green-300 hover:bg-green-100'
                          }`}
                        >
                          {time}
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Session Length */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Session Length
                  </label>
                  <select
                    value={sessionLength}
                    onChange={(e) => setSessionLength(Number(e.target.value))}
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value={30}>30 minutes</option>
                    <option value={60}>1 hour</option>
                    <option value={90}>1.5 hours</option>
                    <option value={120}>2 hours</option>
                  </select>
                </div>

                {/* Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Message (Optional)
                  </label>
                  <textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Tell the tutor what you'd like to work on..."
                    className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={4}
                  />
                </div>

                <Button 
                  onClick={handleBooking}
                  disabled={bookingLoading || !selectedDate || !selectedTime || !selectedSubject}
                  className="w-full"
                  size="lg"
                >
                  {bookingLoading ? 'Sending Request...' : 'Send Booking Request'}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar - Tutor Info & Summary */}
          <div className="space-y-6">
            {/* Tutor Info */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Your Tutor</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-3">
                  <Avatar className="w-16 h-16">
                    <AvatarImage
                      src={tutor.profile_picture_url || `https://placehold.co/100x100/E0E0E0/808080?text=${tutor.full_name.charAt(0)}`}
                      alt={tutor.full_name}
                    />
                    <AvatarFallback>{tutor.full_name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold text-gray-900">{tutor.full_name}</h3>
                    <p className="text-sm text-gray-600">{tutor.tutor_tier === 'verified' ? 'Verified ProTutor' : 'ProTutor'}</p>
                  </div>
                </div>
                {tutor.bio && (
                  <p className="text-sm text-gray-600 mt-3 line-clamp-3">{tutor.bio}</p>
                )}
              </CardContent>
            </Card>

            {/* Booking Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <DollarSign className="w-5 h-5" />
                  <span>Booking Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Hourly Rate</span>
                  <span className="font-semibold">
                    {formatRate(tutor.hourly_rate)}/hour
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Session Length</span>
                  <span className="font-semibold">{sessionLength} minutes</span>
                </div>
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold">Total</span>
                    <span className="text-xl font-bold text-gray-900">
                      ${calculateTotal().toFixed(2)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Payment will be processed after session completion
                  </p>
                </div>

                {selectedDate && selectedTime && (
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="flex items-center space-x-2 text-sm">
                      <Calendar className="w-4 h-4 text-gray-500" />
                      <span>
                        {new Date(selectedDate).toLocaleDateString('en-US', {
                          weekday: 'short',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm mt-1">
                      <Clock className="w-4 h-4 text-gray-500" />
                      <span>{selectedTime}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Important Notes */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Important Notes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="text-sm text-gray-600">
                  <p>• This is a booking request. The tutor will confirm availability.</p>
                  <p>• Payment will be processed after the tutor accepts.</p>
                  <p>• You can cancel up to 24 hours before the session.</p>
                  <p>• You'll receive a confirmation email once accepted.</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Footer */}
      <Footer />
    </div>
  )
}
