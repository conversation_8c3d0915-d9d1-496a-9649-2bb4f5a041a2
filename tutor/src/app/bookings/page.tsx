'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createClient } from '@/lib/supabase/client'
import { Calendar, Clock, User, MessageCircle, Check, X, DollarSign } from 'lucide-react'
import Logo from '@/components/logo'
import Avatar from '@/components/avatar'
import ProfileDropdown from '@/components/profile-dropdown'
import Footer from '@/components/footer'

interface BookingRequest {
  id: string
  student_id: string
  tutor_id: string
  requested_date: string
  requested_time: string
  subject: string
  session_length: number
  message: string | null
  status: 'pending' | 'confirmed' | 'accepted' | 'declined' | 'completed' | 'cancelled'
  total_amount: number
  decline_reason: string | null
  created_at: string
  student_profile?: {
    id: string
    full_name: string
    email: string
    avatar_url: string | null
    profile_picture_url: string | null
  }
}

export default function BookingsPage() {
  const [user, setUser] = useState<any>(null)
  const [bookingRequests, setBookingRequests] = useState<BookingRequest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'pending' | 'accepted' | 'completed' | 'all'>('pending')
  const supabase = createClient()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      fetchBookingRequests()
    }
  }, [user])

  const checkUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        window.location.href = '/auth'
        return
      }
      setUser(user)
    } catch (error) {
      console.error('Error checking user:', error)
      setError('Failed to authenticate user')
    }
  }

  const fetchBookingRequests = async () => {
    try {
      setLoading(true)

      // First get booking requests
      const { data: bookingData, error: bookingError } = await supabase
        .from('booking_requests')
        .select('*')
        .eq('tutor_id', user.id)
        .order('created_at', { ascending: false })

      if (bookingError) {
        console.error('Booking requests error:', bookingError)
        throw bookingError
      }

      // Then get student profiles for each booking
      const bookingRequestsWithProfiles = await Promise.all(
        (bookingData || []).map(async (booking) => {
          const { data: studentProfile } = await supabase
            .from('profiles')
            .select('id, full_name, email, avatar_url, profile_picture_url')
            .eq('id', booking.student_id)
            .single()

          return {
            ...booking,
            student_profile: studentProfile
          }
        })
      )

      setBookingRequests(bookingRequestsWithProfiles)
    } catch (error) {
      console.error('Error fetching booking requests:', error)
      setError(`Failed to load booking requests: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setLoading(false)
    }
  }

  const handleAcceptBooking = async (bookingId: string) => {
    try {
      // Find the booking request
      const bookingRequest = bookingRequests.find(b => b.id === bookingId)
      if (!bookingRequest) {
        alert('Booking request not found')
        return
      }

      // Show loading state
      const originalText = document.querySelector(`[data-booking-id="${bookingId}"] .accept-btn`)?.textContent
      const acceptBtn = document.querySelector(`[data-booking-id="${bookingId}"] .accept-btn`) as HTMLButtonElement
      if (acceptBtn) {
        acceptBtn.disabled = true
        acceptBtn.textContent = 'Processing...'
      }

      // Step 1: Update booking request status to 'accepted'
      const { error: updateError } = await supabase
        .from('booking_requests')
        .update({ status: 'accepted' })
        .eq('id', bookingId)

      if (updateError) throw updateError

      // Step 2: Get tutor's hourly rate for pricing calculations
      const { data: tutorProfile, error: tutorError } = await supabase
        .from('profiles')
        .select('hourly_rate')
        .eq('id', user.id)
        .single()

      if (tutorError) throw tutorError

      // Step 3: Calculate pricing
      const hourlyRateCents = tutorProfile.hourly_rate || 4500 // Default rate if not set
      const sessionHours = bookingRequest.session_length / 60
      const totalCostCents = Math.round(hourlyRateCents * sessionHours)
      const platformFeePercent = 18 // 18% platform commission
      const platformFeeCents = Math.round(totalCostCents * (platformFeePercent / 100))
      const tutorEarningsCents = totalCostCents - platformFeeCents

      // Step 4: Create scheduled start and end times
      const scheduledStart = new Date(`${bookingRequest.requested_date}T${bookingRequest.requested_time}:00`)
      const scheduledEnd = new Date(scheduledStart.getTime() + (bookingRequest.session_length * 60 * 1000))

      // Step 5: Create confirmed booking in bookings table
      const { data: confirmedBooking, error: bookingError } = await supabase
        .from('bookings')
        .insert({
          student_id: bookingRequest.student_id,
          tutor_id: bookingRequest.tutor_id,
          scheduled_start: scheduledStart.toISOString(),
          scheduled_end: scheduledEnd.toISOString(),
          subject: bookingRequest.subject,
          session_notes: bookingRequest.message || null,
          hourly_rate_cents: hourlyRateCents,
          total_cost_cents: totalCostCents,
          platform_fee_cents: platformFeeCents,
          tutor_earnings_cents: tutorEarningsCents,
          status: 'confirmed'
        })
        .select()
        .single()

      if (bookingError) {
        // Rollback: Update booking request back to pending
        await supabase
          .from('booking_requests')
          .update({ status: 'pending' })
          .eq('id', bookingId)
        throw bookingError
      }

      // Step 6: Success! Refresh the list and notify
      fetchBookingRequests()

      // Show success message with booking details
      const studentName = bookingRequest.student_profile?.full_name || 'Student'
      const sessionDate = new Date(scheduledStart).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
      const sessionTime = new Date(scheduledStart).toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })

      // Step 7: Create a simple notification for the student (in-app notification)
      try {
        await supabase
          .from('notifications')
          .insert({
            user_id: bookingRequest.student_id,
            type: 'booking_accepted',
            title: 'Booking Request Accepted!',
            message: `Your tutoring session for ${bookingRequest.subject} on ${sessionDate} at ${sessionTime} has been confirmed.`,
            data: {
              booking_id: confirmedBooking.id,
              tutor_name: user.email, // We'll improve this later
              session_date: sessionDate,
              session_time: sessionTime,
              subject: bookingRequest.subject
            },
            read: false
          })
      } catch (notificationError) {
        // Don't fail the whole process if notification fails
        console.warn('Failed to create notification:', notificationError)
      }

      alert(`✅ Booking Confirmed!\n\nSession with ${studentName}\n📅 ${sessionDate}\n🕐 ${sessionTime}\n📚 Subject: ${bookingRequest.subject}\n💰 Earnings: $${(tutorEarningsCents / 100).toFixed(2)}\n\nThe student will be notified of the confirmation.`)

    } catch (error) {
      console.error('Error accepting booking:', error)
      alert(`Failed to accept booking request: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      // Reset button state
      const acceptBtn = document.querySelector(`[data-booking-id="${bookingId}"] .accept-btn`) as HTMLButtonElement
      if (acceptBtn) {
        acceptBtn.disabled = false
        acceptBtn.textContent = 'Accept'
      }
    }
  }

  const handleDeclineBooking = async (bookingId: string) => {
    const reason = prompt('Please provide a reason for declining (optional):')
    
    try {
      const { error } = await supabase
        .from('booking_requests')
        .update({ 
          status: 'declined',
          decline_reason: reason || null
        })
        .eq('id', bookingId)

      if (error) throw error
      
      // Refresh the list
      fetchBookingRequests()
      alert('Booking request declined')
    } catch (error) {
      console.error('Error declining booking:', error)
      alert('Failed to decline booking request')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatAmount = (amountInCents: number) => {
    return `$${(amountInCents / 100).toFixed(2)}`
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'accepted': return 'bg-green-100 text-green-800'
      case 'declined': return 'bg-red-100 text-red-800'
      case 'completed': return 'bg-blue-100 text-blue-800'
      case 'cancelled': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const filteredBookings = bookingRequests.filter(booking => {
    if (activeTab === 'all') return true
    return booking.status === activeTab
  })

  if (loading) {
    return (
      <div className="page-container">
        <header className="header-standard">
          <div className="w-full h-full flex justify-between items-center">
            <div className="pl-4">
              <Logo href="/" size="md" />
            </div>
          </div>
        </header>
        
        <div className="flex items-center justify-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="page-container">
      {/* Header */}
      <header className="header-standard">
        <div className="w-full h-full flex justify-between items-center">
          <div className="pl-4">
            <Logo href="/" size="md" />
          </div>
          <div className="nav-right">
            <nav className="nav-links">
              <Link href="/search" className="nav-link">Find Tutors</Link>
              <Link href="/tutoring" className="nav-link">Become a Tutor</Link>
              <Link href="/dashboard" className="nav-link">Dashboard</Link>
              <Link href="/bookings" className="nav-link-active">Bookings</Link>
            </nav>
            <div className="auth-ui">
              {user && <ProfileDropdown user={user} />}
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Booking Requests</h1>
          <p className="text-gray-600">Manage your tutoring session requests</p>
        </div>

        {/* Tabs */}
        <div className="flex space-x-1 mb-8 bg-gray-100 p-1 rounded-lg w-fit">
          {[
            { key: 'pending', label: 'Pending', count: bookingRequests.filter(b => b.status === 'pending').length },
            { key: 'accepted', label: 'Accepted', count: bookingRequests.filter(b => b.status === 'accepted').length },
            { key: 'completed', label: 'Completed', count: bookingRequests.filter(b => b.status === 'completed').length },
            { key: 'all', label: 'All', count: bookingRequests.length }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <p className="text-red-800">{error}</p>
          </div>
        )}

        {filteredBookings.length === 0 ? (
          <div className="text-center py-12">
            <Calendar className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No booking requests</h3>
            <p className="text-gray-600">
              {activeTab === 'pending' 
                ? "You don't have any pending booking requests at the moment."
                : `No ${activeTab} booking requests found.`
              }
            </p>
          </div>
        ) : (
          <div className="space-y-6">
            {filteredBookings.map((booking) => (
              <Card key={booking.id}>
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div className="flex items-center space-x-4">
                      <div className="professional-image-frame large w-28 h-28 rounded-xl overflow-hidden flex-shrink-0">
                        <div className="image-enhancement-wrapper w-full h-full relative">
                          <Avatar
                            src={booking.student_profile?.profile_picture_url || booking.student_profile?.avatar_url}
                            name={booking.student_profile?.full_name || 'Student'}
                            id={booking.student_profile?.id || booking.id}
                            size="xl"
                            className="w-full h-full"
                          />
                        </div>
                      </div>
                      <div>
                        <CardTitle className="text-xl font-semibold text-gray-900">
                          📚 Booking Request from {booking.student_profile?.full_name || 'Student'}
                        </CardTitle>
                        <CardDescription className="text-base text-gray-600 mt-1">
                          {booking.student_profile?.email}
                        </CardDescription>
                        <div className="mt-2 text-sm text-gray-500">
                          Student ID: {booking.student_profile?.id?.slice(0, 8)}...
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(booking.status)}>
                      {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <Calendar className="w-4 h-4 text-gray-500" />
                        <span>{formatDate(booking.requested_date)}</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <Clock className="w-4 h-4 text-gray-500" />
                        <span>{booking.requested_time} ({booking.session_length} minutes)</span>
                      </div>
                      <div className="flex items-center space-x-2 text-sm">
                        <DollarSign className="w-4 h-4 text-gray-500" />
                        <span>{formatAmount(booking.total_amount)}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <p className="text-sm"><strong>Subject:</strong> {booking.subject}</p>
                      {booking.message && (
                        <div>
                          <p className="text-sm font-medium">Message:</p>
                          <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">{booking.message}</p>
                        </div>
                      )}
                      {booking.decline_reason && (
                        <div>
                          <p className="text-sm font-medium text-red-700">Decline Reason:</p>
                          <p className="text-sm text-red-600 bg-red-50 p-2 rounded">{booking.decline_reason}</p>
                        </div>
                      )}
                    </div>
                  </div>

                  {booking.status === 'pending' && (
                    <div className="flex space-x-3 pt-4 border-t" data-booking-id={booking.id}>
                      <Button
                        onClick={() => handleAcceptBooking(booking.id)}
                        className="bg-green-600 hover:bg-green-700 accept-btn"
                      >
                        <Check className="w-4 h-4 mr-2" />
                        Accept
                      </Button>
                      <Button
                        variant="outline"
                        onClick={() => handleDeclineBooking(booking.id)}
                        className="border-red-300 text-red-700 hover:bg-red-50"
                      >
                        <X className="w-4 h-4 mr-2" />
                        Decline
                      </Button>
                      <Button variant="outline">
                        <MessageCircle className="w-4 h-4 mr-2" />
                        Message Student
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <Footer />
    </div>
  )
}
