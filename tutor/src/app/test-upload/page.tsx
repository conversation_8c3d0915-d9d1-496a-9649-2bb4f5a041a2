'use client'

import { useState, useEffect } from 'react'
import { createClient } from '@/lib/supabase/client'
import Avatar from '@/components/avatar'
import ProfilePictureUpload from '@/components/profile-picture-upload'
import type { User } from '@supabase/supabase-js'

export default function TestUploadPage() {
  const [user, setUser] = useState<User | null>(null)
  const [profile, setProfile] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [showUpload, setShowUpload] = useState(false)
  const supabase = createClient()

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      
      if (user) {
        // Get user profile
        const { data: profileData } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single()
        
        setProfile(profileData)
      }
    } catch (error) {
      console.error('Error checking user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleUploadSuccess = (newAvatarUrl: string) => {
    setProfile((prev: any) => ({ ...prev, avatar_url: newAvatarUrl }))
    setShowUpload(false)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Please Sign In</h1>
          <p className="text-gray-600 mb-6">You need to be signed in to test profile picture upload.</p>
          <a 
            href="/auth" 
            className="bg-gray-900 text-white px-6 py-2 rounded-lg hover:bg-gray-800"
          >
            Sign In
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Profile Picture Upload Test
          </h1>
          <p className="text-gray-600">
            Test the Filen integration for profile pictures
          </p>
        </div>

        {/* Current Profile Card */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Profile</h2>
          
          <div className="flex items-center space-x-4 mb-6">
            <Avatar
              src={profile?.avatar_url}
              name={profile?.full_name || user.email?.split('@')[0] || 'User'}
              id={user.id}
              size="xl"
              showUploadOnHover={true}
              onUploadClick={() => setShowUpload(true)}
            />
            
            <div>
              <h3 className="text-lg font-medium text-gray-900">
                {profile?.full_name || user.email?.split('@')[0] || 'User'}
              </h3>
              <p className="text-gray-600">{user.email}</p>
              <p className="text-sm text-gray-500 mt-1">
                {profile?.avatar_url ? 'Custom profile picture' : 'Generated avatar'}
              </p>
            </div>
          </div>

          <button
            onClick={() => setShowUpload(!showUpload)}
            className="bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800 transition-colors"
          >
            {showUpload ? 'Cancel Upload' : 'Change Profile Picture'}
          </button>
        </div>

        {/* Upload Section */}
        {showUpload && (
          <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Upload New Picture</h2>
            
            <ProfilePictureUpload
              userId={user.id}
              onUploadSuccess={handleUploadSuccess}
            />
          </div>
        )}

        {/* Avatar Sizes Demo */}
        <div className="bg-white rounded-lg shadow-sm border p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Avatar Sizes Preview</h2>
          
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <Avatar
                src={profile?.avatar_url}
                name={profile?.full_name || user.email?.split('@')[0] || 'User'}
                id={user.id}
                size="sm"
              />
              <p className="text-xs text-gray-500 mt-1">Small</p>
            </div>
            
            <div className="text-center">
              <Avatar
                src={profile?.avatar_url}
                name={profile?.full_name || user.email?.split('@')[0] || 'User'}
                id={user.id}
                size="md"
              />
              <p className="text-xs text-gray-500 mt-1">Medium</p>
            </div>
            
            <div className="text-center">
              <Avatar
                src={profile?.avatar_url}
                name={profile?.full_name || user.email?.split('@')[0] || 'User'}
                id={user.id}
                size="lg"
              />
              <p className="text-xs text-gray-500 mt-1">Large</p>
            </div>
            
            <div className="text-center">
              <Avatar
                src={profile?.avatar_url}
                name={profile?.full_name || user.email?.split('@')[0] || 'User'}
                id={user.id}
                size="xl"
              />
              <p className="text-xs text-gray-500 mt-1">Extra Large</p>
            </div>
          </div>
        </div>

        {/* Technical Info */}
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Technical Details</h2>
          
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Storage:</span>
              <span className="font-mono">Filen.io (10GB free)</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Max file size:</span>
              <span className="font-mono">5MB</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Supported formats:</span>
              <span className="font-mono">JPEG, PNG, WebP</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Current avatar URL:</span>
              <span className="font-mono text-xs break-all">
                {profile?.avatar_url || 'Generated avatar'}
              </span>
            </div>
          </div>
        </div>

        {/* Back to Search */}
        <div className="text-center mt-8">
          <a 
            href="/search" 
            className="text-gray-600 hover:text-gray-900 text-sm"
          >
            ← Back to Search Page
          </a>
        </div>
      </div>
    </div>
  )
}
