'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { createClient } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useRouter } from 'next/navigation'
import EnhancedImageUpload from '@/components/enhanced-image-upload'
import AvailabilityEditor from '@/components/availability-editor'

interface Profile {
  id: string
  full_name: string
  email: string
  role: string
  tutor_tier: 'standard' | 'verified'
  bio?: string
  subjects?: string[]
  education?: string
  experience_years?: number
  certifications?: string[]
  languages?: string[]
}

const SUBJECT_OPTIONS = [
  { value: 'Mathematics', label: 'Mathematics (IB Math, AP Calculus, Statistics)' },
  { value: 'Sciences', label: 'Sciences (Physics, Chemistry, Biology)' },
  { value: 'Literature', label: 'Literature (English A/B, Language Arts)' },
  { value: 'Social Studies', label: 'Social Studies (History, Geography, Economics)' },
  { value: 'Languages', label: 'Languages (Foreign Languages, ESL)' }
]

export default function TutorProfilePage() {
  const [profile, setProfile] = useState<Profile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState('')
  
  // Form fields
  const [bio, setBio] = useState('')
  const [subjects, setSubjects] = useState<string[]>([])
  const [education, setEducation] = useState('')
  const [experienceYears, setExperienceYears] = useState<number>(0)
  const [certifications, setCertifications] = useState<string[]>([''])
  const [languages, setLanguages] = useState<string[]>(['English'])
  const [hourlyRate, setHourlyRate] = useState<number>(45)
  const [hasTutorRole, setHasTutorRole] = useState(false)
  const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(null)

  const supabase = createClient()
  const router = useRouter()

  useEffect(() => {
    async function loadProfile() {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) {
        router.push('/auth')
        return
      }

      const { data: profile } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (!profile) {
        router.push('/dashboard')
        return
      }

      // Check if user has tutor role - if not, they can apply
      const userHasTutorRole = profile.roles?.includes('tutor')
      setHasTutorRole(userHasTutorRole)

      setProfile(profile)

      // Populate form with existing data if user is already a tutor
      if (userHasTutorRole) {
        setBio(profile.bio || '')
        setSubjects(profile.subjects || [])
        setEducation(profile.education || '')
        setExperienceYears(profile.experience_years || 0)
        setCertifications(profile.certifications || [''])
        setLanguages(profile.languages || ['English'])
        setHourlyRate(profile.hourly_rate ? profile.hourly_rate / 100 : 45)
        setProfilePictureUrl(profile.profile_picture_url || null)
      }

      setLoading(false)
    }

    loadProfile()
  }, [supabase, router])

  const handleSubjectToggle = (subject: string) => {
    setSubjects(prev => 
      prev.includes(subject) 
        ? prev.filter(s => s !== subject)
        : [...prev, subject]
    )
  }

  const handleCertificationChange = (index: number, value: string) => {
    const newCertifications = [...certifications]
    newCertifications[index] = value
    setCertifications(newCertifications)
  }

  const addCertification = () => {
    setCertifications([...certifications, ''])
  }

  const removeCertification = (index: number) => {
    setCertifications(certifications.filter((_, i) => i !== index))
  }

  const handleLanguageChange = (index: number, value: string) => {
    const newLanguages = [...languages]
    newLanguages[index] = value
    setLanguages(newLanguages)
  }

  const addLanguage = () => {
    setLanguages([...languages, ''])
  }

  const removeLanguage = (index: number) => {
    if (languages.length > 1) {
      setLanguages(languages.filter((_, i) => i !== index))
    }
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    setSaving(true)
    setMessage('')

    try {
      // Validate required fields for tutor application
      if (!hasTutorRole) {
        if (!bio.trim() || bio.trim().length < 50) {
          throw new Error('Bio must be at least 50 characters long')
        }
        if (subjects.length === 0) {
          throw new Error('Please select at least one subject')
        }
        if (!education.trim() || !/university|college|bachelor|master|phd|doctorate/i.test(education)) {
          throw new Error('Please include university or college education')
        }
        if (experienceYears < 2) {
          throw new Error('Minimum 2 years of teaching experience required')
        }
        if (!certifications.some(c => c.trim())) {
          throw new Error('Please add at least one certification')
        }
        if (hourlyRate < 15) {
          throw new Error('Minimum hourly rate is $15')
        }
      }

      // Update profile with tutor information
      const { error: profileError } = await supabase
        .from('profiles')
        .update({
          bio: bio.trim(),
          subjects: subjects.filter(s => s),
          education: education.trim(),
          experience_years: experienceYears,
          certifications: certifications.filter(c => c.trim()),
          languages: languages.filter(l => l.trim()),
          hourly_rate: Math.round(hourlyRate * 100), // Store in cents
          profile_picture_url: profilePictureUrl
        })
        .eq('id', profile?.id)

      if (profileError) throw profileError

      // If user doesn't have tutor role yet, request it
      if (!hasTutorRole) {
        const { error: roleError } = await supabase
          .rpc('add_user_role', {
            user_id: profile?.id,
            new_role: 'tutor'
          })

        if (roleError) throw roleError

        setMessage('Tutor application submitted successfully! Pending admin approval.')
        setTimeout(() => router.push('/dashboard'), 3000)
      } else {
        setMessage('Profile updated successfully!')
        setTimeout(() => router.push('/dashboard'), 2000)
      }
    } catch (error) {
      setMessage(error instanceof Error ? error.message : 'Failed to save profile')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Loading profile...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background to-secondary/20">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/">
            <h1 className="text-2xl font-bold cursor-pointer hover:text-primary">ProTutor</h1>
          </Link>
          <nav className="flex items-center space-x-4">
            <Link href="/dashboard">
              <Button variant="outline">Dashboard</Button>
            </Link>
          </nav>
        </div>
      </header>
      
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="bg-card rounded-lg p-6 border">
            <div className="mb-6">
              <h1 className="text-2xl font-bold mb-2">
                {hasTutorRole ? 'Edit Your Tutor Profile' : 'Apply to Become a Tutor'}
              </h1>
              <p className="text-muted-foreground">
                {hasTutorRole
                  ? 'Update your profile to help students find you and start accepting bookings.'
                  : 'Complete this application to become a tutor on our platform. All fields are required for approval.'
                }
              </p>
              {profile?.tutor_tier && (
                <div className="mt-2">
                  <span className={`px-2 py-1 rounded text-xs ${
                    profile.tutor_tier === 'verified' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {profile.tutor_tier === 'verified' ? '✓ Verified Tutor' : 'Standard Tutor'}
                  </span>
                </div>
              )}

              {!hasTutorRole && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-semibold text-blue-800 mb-2">Application Requirements</h3>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• <strong>Bio:</strong> At least 50 characters describing your teaching approach</li>
                    <li>• <strong>Subjects:</strong> Select from our 5 main subject categories</li>
                    <li>• <strong>Education:</strong> Must include university or college background</li>
                    <li>• <strong>Experience:</strong> Minimum 2 years of teaching/tutoring experience</li>
                    <li>• <strong>Certifications:</strong> At least one relevant certification</li>
                    <li>• <strong>Rate:</strong> Set your hourly rate (minimum $15/hour)</li>
                  </ul>
                </div>
              )}
            </div>

            <form onSubmit={handleSave} className="space-y-6">
              {/* Profile Picture */}
              <EnhancedImageUpload
                currentImageUrl={profilePictureUrl}
                onImageChange={setProfilePictureUrl}
                userId={profile?.id || ''}
                disabled={saving}
                size="medium"
                aspectRatio={4/3}
                cropShape="rect"
              />

              {/* Bio */}
              <div id="bio" className="space-y-2">
                <Label htmlFor="bio">About You</Label>
                <Textarea
                  id="bio"
                  placeholder="Tell students about your teaching experience, approach, and what makes you a great tutor..."
                  value={bio}
                  onChange={(e) => setBio(e.target.value)}
                  rows={4}
                  required
                />
              </div>

              {/* Subjects */}
              <div id="subjects" className="space-y-2">
                <Label>Subjects You Teach</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {SUBJECT_OPTIONS.map((subject) => (
                    <label key={subject.value} className="flex items-center space-x-2 p-2 border rounded hover:bg-secondary/50 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={subjects.includes(subject.value)}
                        onChange={() => handleSubjectToggle(subject.value)}
                        className="rounded"
                      />
                      <span className="text-sm">{subject.label}</span>
                    </label>
                  ))}
                </div>
                {subjects.length === 0 && (
                  <p className="text-sm text-red-600">Please select at least one subject</p>
                )}
              </div>

              {/* Education */}
              <div id="education" className="space-y-2">
                <Label htmlFor="education">Education Background</Label>
                <Input
                  id="education"
                  placeholder="e.g. Bachelor's in Mathematics from University of Oxford"
                  value={education}
                  onChange={(e) => setEducation(e.target.value)}
                  required
                />
              </div>

              {/* Experience */}
              <div id="experience" className="space-y-2">
                <Label htmlFor="experience">Years of Teaching/Tutoring Experience</Label>
                <Input
                  id="experience"
                  type="number"
                  min="0"
                  max="50"
                  value={experienceYears}
                  onChange={(e) => setExperienceYears(parseInt(e.target.value) || 0)}
                  required
                />
              </div>

              {/* Hourly Rate */}
              <div id="pricing" className="space-y-2">
                <Label htmlFor="hourlyRate">Hourly Rate (USD)</Label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                  <Input
                    id="hourlyRate"
                    type="number"
                    min="15"
                    max="200"
                    step="5"
                    value={hourlyRate}
                    onChange={(e) => setHourlyRate(parseInt(e.target.value) || 45)}
                    className="pl-8"
                    required
                  />
                </div>
                <p className="text-sm text-gray-600">
                  Set your hourly rate. Most tutors charge between $25-$100 per hour.
                </p>
              </div>

              {/* Weekly Availability */}
              <div id="availability" className="space-y-2">
                <Label>Weekly Availability</Label>
                <AvailabilityEditor />
              </div>

              {/* Certifications */}
              <div id="certifications" className="space-y-2">
                <Label>Certifications & Qualifications</Label>
                {certifications.map((cert, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="e.g. IB Teacher Training Certificate"
                      value={cert}
                      onChange={(e) => handleCertificationChange(index, e.target.value)}
                    />
                    {certifications.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeCertification(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={addCertification}>
                  Add Certification
                </Button>
              </div>

              {/* Languages */}
              <div className="space-y-2">
                <Label>Languages You Speak</Label>
                {languages.map((lang, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      placeholder="e.g. English"
                      value={lang}
                      onChange={(e) => handleLanguageChange(index, e.target.value)}
                      required={index === 0}
                    />
                    {languages.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeLanguage(index)}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                ))}
                <Button type="button" variant="outline" size="sm" onClick={addLanguage}>
                  Add Language
                </Button>
              </div>

              {message && (
                <div className={`text-center text-sm ${
                  message.includes('successfully') ? 'text-green-600' : 'text-red-600'
                }`}>
                  {message}
                </div>
              )}

              <div className="flex gap-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/dashboard')}
                  disabled={saving}
                >
                  Back to Dashboard
                </Button>
                <Button 
                  type="submit" 
                  disabled={saving || subjects.length === 0 || !hourlyRate || hourlyRate < 15}
                  className="flex-1"
                >
                  {saving
                    ? (hasTutorRole ? 'Saving...' : 'Submitting Application...')
                    : (hasTutorRole ? 'Save Profile' : 'Submit Tutor Application')
                  }
                </Button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  )
}