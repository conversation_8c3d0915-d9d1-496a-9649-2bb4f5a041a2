/* Professional Image Enhancement Styles */

.professional-image-frame {
  /* Enhanced shadow with multiple layers for depth */
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  
  /* Subtle border for definition */
  border: 1px solid rgba(255, 255, 255, 0.2);
  
  /* Background gradient for better contrast */
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  
  /* Professional positioning */
  position: relative;
}

.professional-image-frame:hover {
  /* Enhanced hover shadow for premium feel */
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.15),
    0 10px 10px -5px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  
  /* Subtle lift effect */
  transform: translateY(-2px);
}

.image-enhancement-wrapper {
  /* Professional image filters */
  filter: 
    contrast(1.05)
    saturate(1.1)
    brightness(1.02);
  
  /* Smooth transitions */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.image-enhancement-wrapper:hover {
  /* Enhanced filters on hover */
  filter: 
    contrast(1.1)
    saturate(1.15)
    brightness(1.05)
    drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

/* Professional lighting effect overlay */
.professional-image-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at 30% 20%,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.05) 40%,
    transparent 70%
  );
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
}

/* Studio lighting effect on hover */
.professional-image-frame:hover::before {
  background: radial-gradient(
    circle at 30% 20%,
    rgba(255, 255, 255, 0.25) 0%,
    rgba(255, 255, 255, 0.1) 40%,
    rgba(0, 0, 0, 0.05) 100%
  );
}

/* Professional vignette effect */
.professional-image-frame::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center,
    transparent 30%,
    rgba(0, 0, 0, 0.03) 70%,
    rgba(0, 0, 0, 0.08) 100%
  );
  pointer-events: none;
  z-index: 2;
  border-radius: inherit;
  opacity: 0.7;
}

.professional-image-frame:hover::after {
  opacity: 0.5;
}

/* Enhanced frame for larger profile images */
.professional-image-frame.large {
  box-shadow: 
    0 8px 12px -2px rgba(0, 0, 0, 0.12),
    0 4px 8px -2px rgba(0, 0, 0, 0.08),
    inset 0 2px 0 rgba(255, 255, 255, 0.15);
}

.professional-image-frame.large:hover {
  box-shadow: 
    0 25px 35px -5px rgba(0, 0, 0, 0.18),
    0 15px 15px -5px rgba(0, 0, 0, 0.12),
    inset 0 2px 0 rgba(255, 255, 255, 0.25);
  
  transform: translateY(-3px);
}

/* Premium gold accent for verified tutors */
.professional-image-frame.verified {
  border: 2px solid rgba(251, 191, 36, 0.3);
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(251, 191, 36, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.professional-image-frame.verified:hover {
  border-color: rgba(251, 191, 36, 0.5);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.15),
    0 10px 10px -5px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(251, 191, 36, 0.2),
    0 0 20px rgba(251, 191, 36, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Professional image inside frame */
.professional-image-frame img {
  /* Ensure crisp image rendering */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  
  /* Professional color adjustments */
  filter: 
    contrast(1.05)
    saturate(1.08)
    brightness(1.02)
    hue-rotate(2deg);
}

.professional-image-frame:hover img {
  filter: 
    contrast(1.08)
    saturate(1.12)
    brightness(1.04)
    hue-rotate(1deg);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .professional-image-frame {
    box-shadow: 
      0 2px 4px -1px rgba(0, 0, 0, 0.1),
      0 1px 2px -1px rgba(0, 0, 0, 0.06);
  }
  
  .professional-image-frame:hover {
    transform: translateY(-1px);
    box-shadow: 
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}
