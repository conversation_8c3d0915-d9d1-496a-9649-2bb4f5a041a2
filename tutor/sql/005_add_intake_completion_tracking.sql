-- Migration: Add Intake Completion Tracking
-- Created: 2025-01-20
-- Purpose: Add intake completion status and tracking to profiles table

-- Create intake_status enum
DO $$ BEGIN
  CREATE TYPE intake_status AS ENUM ('incomplete', 'complete', 'locked');
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Add intake completion tracking fields to profiles table
DO $$
BEGIN
  -- Check and add intake_status column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='intake_status') THEN
    ALTER TABLE public.profiles ADD COLUMN intake_status intake_status DEFAULT 'incomplete';
    RAISE NOTICE 'Added intake_status column';
  ELSE
    RAISE NOTICE 'intake_status column already exists';
  END IF;

  -- Check and add intake_completed_at column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='intake_completed_at') THEN
    ALTER TABLE public.profiles ADD COLUMN intake_completed_at TIMESTAMPTZ;
    RAISE NOTICE 'Added intake_completed_at column';
  ELSE
    RAISE NOTICE 'intake_completed_at column already exists';
  END IF;

  -- Check and add intake_completed_by column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='intake_completed_by') THEN
    ALTER TABLE public.profiles ADD COLUMN intake_completed_by UUID REFERENCES public.profiles(id);
    RAISE NOTICE 'Added intake_completed_by column';
  ELSE
    RAISE NOTICE 'intake_completed_by column already exists';
  END IF;

  -- Check and add intake_notes column
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='profiles' AND column_name='intake_notes') THEN
    ALTER TABLE public.profiles ADD COLUMN intake_notes TEXT;
    RAISE NOTICE 'Added intake_notes column';
  ELSE
    RAISE NOTICE 'intake_notes column already exists';
  END IF;
END $$;

-- Create function to mark intake as complete
CREATE OR REPLACE FUNCTION public.complete_intake(
  p_profile_id UUID,
  p_completed_by UUID,
  p_notes TEXT DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.profiles 
  SET 
    intake_status = 'complete'::intake_status,
    intake_completed_at = NOW(),
    intake_completed_by = p_completed_by,
    intake_notes = COALESCE(p_notes, intake_notes),
    updated_at = NOW()
  WHERE id = p_profile_id;
END;
$$;

-- Create function to unlock intake for editing
CREATE OR REPLACE FUNCTION public.unlock_intake(
  p_profile_id UUID,
  p_unlocked_by UUID
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.profiles 
  SET 
    intake_status = 'incomplete'::intake_status,
    intake_completed_at = NULL,
    intake_completed_by = NULL,
    updated_at = NOW()
  WHERE id = p_profile_id;
END;
$$;

-- Create function to lock intake (prevent editing)
CREATE OR REPLACE FUNCTION public.lock_intake(
  p_profile_id UUID,
  p_locked_by UUID,
  p_notes TEXT DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE public.profiles 
  SET 
    intake_status = 'locked'::intake_status,
    intake_completed_at = COALESCE(intake_completed_at, NOW()),
    intake_completed_by = COALESCE(intake_completed_by, p_locked_by),
    intake_notes = COALESCE(p_notes, intake_notes),
    updated_at = NOW()
  WHERE id = p_profile_id;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION public.complete_intake(UUID, UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.unlock_intake(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.lock_intake(UUID, UUID, TEXT) TO authenticated;

-- Create index for intake status queries
CREATE INDEX IF NOT EXISTS idx_profiles_intake_status ON profiles(intake_status);
CREATE INDEX IF NOT EXISTS idx_profiles_intake_completed_at ON profiles(intake_completed_at);

-- Set existing approved tutors and students to complete status
-- (They've already been through the system)
UPDATE public.profiles 
SET 
  intake_status = 'complete',
  intake_completed_at = approved_at,
  intake_completed_by = id -- Self-completed for existing users
WHERE 
  (tutor_status = 'approved' OR student_status = 'approved')
  AND intake_status = 'incomplete';
