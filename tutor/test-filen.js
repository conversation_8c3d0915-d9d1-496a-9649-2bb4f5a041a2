// Simple Node.js script to test Filen SDK directly
const FilenSDK = require('@filen/sdk').default;
const fs = require('fs');
const path = require('path');

async function testFilenUpload() {
  try {
    console.log('🔧 Initializing Filen SDK...');
    
    const filen = new FilenSDK({
      email: '<EMAIL>',
      password: '278bUn654,',
    });

    console.log('🔑 Logging in to Filen...');
    await filen.login();
    console.log('✅ Successfully logged in to Filen!');

    // Create a test image buffer (1x1 pixel PNG)
    const testImageBuffer = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x02, 0x00, 0x00, 0x00, 0x90, 0x77, 0x53, 0xDE, 0x00, 0x00, 0x00,
      0x0C, 0x49, 0x44, 0x41, 0x54, 0x08, 0xD7, 0x63, 0xF8, 0x00, 0x00, 0x00,
      0x01, 0x00, 0x01, 0x35, 0x2F, 0xB5, 0x68, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    console.log('📁 Creating profile-pictures folder...');
    try {
      await filen.fs().mkdir({
        name: 'profile-pictures',
        parent: 'base',
      });
      console.log('✅ Profile pictures folder created!');
    } catch (error) {
      console.log('ℹ️ Profile pictures folder already exists or error:', error.message);
    }

    console.log('📤 Uploading test image...');
    const uploadResult = await filen.fs().upload({
      file: testImageBuffer,
      name: 'test-profile-image.png',
      parent: 'profile-pictures',
    });

    console.log('✅ Upload successful!', uploadResult);

    console.log('🔗 Creating public link...');
    const publicLink = await filen.fs().link({
      uuid: uploadResult.uuid,
      type: 'file',
      expiration: 'never',
      downloadBtn: false,
      password: '',
    });

    console.log('✅ Public link created:', publicLink.url);

    console.log('🎉 Test completed successfully!');
    console.log('📋 Summary:');
    console.log('   - Filen login: ✅');
    console.log('   - Folder creation: ✅');
    console.log('   - File upload: ✅');
    console.log('   - Public link: ✅');
    console.log('   - Image URL:', publicLink.url);

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
}

// Run the test
testFilenUpload();
